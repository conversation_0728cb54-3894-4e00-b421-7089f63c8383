#
# Manticore Search configuration file
#
# This is the main configuration file for Manticore Search
# For more information, see: https://manual.manticoresearch.com/
#

#############################################################################
## indexer settings
#############################################################################

indexer
{
    # memory limit, in bytes, kiloytes (16384K) or megabytes (256M)
    # optional, default is 128M, max is 2047M, recommended is 256M to 1024M
    mem_limit = 128M
}

#############################################################################
## searchd settings
#############################################################################

searchd
{
    # [hostname:]port[:protocol], or /unix/socket/path to listen on
    # known protocols are 'sphinx' (SphinxAPI) and 'mysql41' (SphinxQL)
    # multi-value, multiple listen points are allowed
    # optional, defaults are 9312:sphinx and 9306:mysql41, as below
    #
    # listen on all interfaces for SphinxQL (MySQL protocol)
    listen = 9306:mysql41
    # listen on all interfaces for SphinxAPI
    listen = 9312
    # listen on all interfaces for HTTP
    listen = 9308:http

    # log file, searchd run info is logged here
    # optional, default is 'searchd.log'
    log = /var/log/manticore/searchd.log

    # query log file, all search queries are logged here
    # optional, default is empty (do not log queries)
    query_log = /var/log/manticore/query.log

    # client read timeout, seconds
    # optional, default is 5
    read_timeout = 5

    # request timeout, seconds
    # optional, default is 5 minutes
    client_timeout = 300

    # maximum amount of children to fork (concurrent searches to run)
    # optional, default is 0 (unlimited)
    max_children = 30

    # PID file, searchd process ID file name
    # mandatory
    pid_file = /var/run/manticore/searchd.pid

    # seamless rotate, prevents rotate stalls if precaching huge datasets
    # optional, default is 1
    seamless_rotate = 1

    # whether to forcibly preopen all indexes on startup
    # optional, default is 1 (preopen everything)
    preopen_indexes = 1

    # whether to unlink .old index copies on successful rotation.
    # optional, default is 1 (do unlink)
    unlink_old = 1

    # attribute updates periodic flush timeout, seconds
    # updates will be automatically dumped to disk this frequently
    # optional, default is 0 (disable periodic flush)
    attr_flush_period = 900

    # MVA updates pool size
    # shared pool for all indexes
    # optional, default size is 1M
    mva_updates_pool = 1M

    # max allowed network packet size
    # limits both query packets from clients, and responses from agents
    # optional, default size is 8M
    max_packet_size = 8M

    # max allowed per-query filter count
    # optional, default is 256
    max_filters = 256

    # max allowed per-filter values count
    # optional, default is 4096
    max_filter_values = 4096

    # socket listen queue length
    # optional, default is 5
    listen_backlog = 5

    # per-keyword read buffer size
    # optional, default is 256K
    read_buffer = 256K

    # unhinted read size (currently used when reading hits)
    # optional, default is 32K
    read_unhinted = 32K

    # max allowed threads count (deprecated, removed in newer versions)
    # optional, default is 0 (unlimited)
    # max_threads = 0

    # binlog files path; use empty string to disable binlog
    # optional, default is build-time configured data directory
    binlog_path = /var/lib/manticore/binlog

    # binlog flush/sync mode
    # 0 - flush and sync every second
    # 1 - flush and sync every transaction
    # 2 - flush every transaction, sync every second
    # optional, default is 2
    binlog_flush = 2

    # binlog per-file size limit
    # optional, default is 128M, 0 means 'no limit'
    binlog_max_log_size = 256M

    # per-thread stack size, only affects workers
    # optional, default is 64K
    thread_stack = 128K

    # per-query expansion limit (for dict=keywords prefix expansion)
    # optional, default is 0 (no limit)
    expansion_limit = 1000

    # RT RAM chunks flush period
    # optional, default is 0 (no periodic flush)
    rt_flush_period = 900

    # query cache size, per-thread
    # optional, default is 16M
    qcache_max_bytes = 16M

    # query cache entry TTL
    # optional, default is 60 seconds
    qcache_ttl_sec = 60

    # query cache check period
    # optional, default is 60 seconds  
    qcache_thresh_msec = 3000

    # shutdown timeout
    # optional, default is 3 seconds
    shutdown_timeout = 3

    # watchdog thread
    # optional, default is 1 (watchdog enabled)
    watchdog = 1

    # compatibility with MySQL SHOW VARIABLES
    # optional, default is 0 (MySQL compatibility disabled)
    compat_sphinxql_magics = 0

    # workers = thread_pool # threaded mode
    workers = threads # threaded mode

    # dist_threads = 0 # disable multi-threading for distributed queries
    dist_threads = 0

    # subtree_docs_cache and subtree_hits_cache in MB
    # optional, default is 0 (disable caching)
    subtree_docs_cache = 0
    subtree_hits_cache = 0

    # predicted_time_costs as time in nanoseconds to predict query time
    # optional, default is "doc=64, hit=48, skip=2048, match=64"
    predicted_time_costs = doc=64, hit=48, skip=2048, match=64

    # access_plain_attrs = mmap_preread
    # access_blob_attrs = mmap_preread
    # access_doclists = file
    # access_hitlists = file
}

#############################################################################
## common settings
#############################################################################

common
{
    # lemmatizer dictionaries base path
    # optional, defaut is /usr/local/share (see ./configure --datadir)
    lemmatizer_base = /usr/share/manticore

    # path to RLP root directory
    # optional, defaut is /usr/local/share (see ./configure --datadir)  
    rlp_root = /usr/share/manticore/rlp

    # path to RLP environment directory
    # optional, defaut is /usr/local/share (see ./configure --datadir)
    rlp_environment = /usr/share/manticore/rlp-environment

    # OnDiskDictionary dictionary base path
    # optional, defaut is /usr/local/share (see ./configure --datadir)
    plugin_dir = /usr/lib/manticore
}

#############################################################################
## sample real-time table definition
#############################################################################

table rt
{
    type = rt
    path = /var/lib/manticore/rt

    # RT table fields
    rt_field = title
    rt_field = content

    # RT table attributes  
    rt_attr_uint = gid
    rt_attr_bigint = id
    rt_attr_timestamp = date_added

    # morphology
    morphology = stem_en

    # minimum word length to index
    min_word_len = 1

    # charset encoding type
    charset_type = utf-8
}

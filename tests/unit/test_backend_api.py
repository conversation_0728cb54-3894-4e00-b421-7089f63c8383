"""
Backend API 路由单元测试
"""

import pytest
import uuid
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlmodel import Session, create_engine
from sqlalchemy.pool import StaticPool

# 导入要测试的应用和模型
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend'))

from app.main import app
from app.models import User, Document
from app.core.config import settings


@pytest.fixture
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture
def session(engine):
    """创建测试数据库会话"""
    from app.models.base import SQLModel
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
def test_user(session):
    """创建测试用户"""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        is_active=True
    )
    session.add(user)
    session.commit()
    session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user):
    """创建认证头"""
    # 这里应该生成真实的JWT token，但为了简化测试，我们使用模拟
    token = "test_token"
    return {"Authorization": f"Bearer {token}"}


class TestHealthEndpoints:
    """健康检查端点测试"""
    
    def test_root_endpoint(self, client):
        """测试根端点"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_health_check(self, client):
        """测试健康检查端点"""
        response = client.get("/api/v1/utils/health-check")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"


class TestAuthenticationEndpoints:
    """认证端点测试"""
    
    @patch('app.api.routes.login.crud.authenticate')
    def test_login_success(self, mock_authenticate, client, test_user):
        """测试成功登录"""
        mock_authenticate.return_value = test_user

        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword"
        }

        response = client.post("/api/v1/login/access-token", data=login_data)
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    @patch('app.api.routes.login.crud.authenticate')
    def test_login_failure(self, mock_authenticate, client):
        """测试登录失败"""
        mock_authenticate.return_value = False

        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }

        response = client.post("/api/v1/login/access-token", data=login_data)
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data


class TestUserEndpoints:
    """用户端点测试"""
    
    @patch('app.api.deps.get_current_user')
    def test_get_current_user(self, mock_get_current_user, client, test_user, auth_headers):
        """测试获取当前用户"""
        mock_get_current_user.return_value = test_user

        response = client.get("/api/v1/users/me", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["full_name"] == test_user.full_name

    @patch('app.api.deps.get_current_user')
    @patch('app.crud.update_user')
    def test_update_current_user(self, mock_update_user, mock_get_current_user, client, test_user, auth_headers):
        """测试更新当前用户"""
        mock_get_current_user.return_value = test_user
        
        updated_user = User(
            id=test_user.id,
            email="<EMAIL>",
            hashed_password=test_user.hashed_password,
            full_name="Updated User",
            is_active=True
        )
        mock_update_user.return_value = updated_user
        
        update_data = {
            "full_name": "Updated User",
            "email": "<EMAIL>"
        }
        
        response = client.patch("/api/v1/users/me", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "Updated User"
        assert data["email"] == "<EMAIL>"


class TestDocumentEndpoints:
    """文档端点测试"""
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_create_document(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试创建文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        created_doc = Document(
            id=uuid.uuid4(),
            title="Test Document",
            content="Test content",
            file_type="txt",
            size=100,
            owner_id=test_user.id
        )
        mock_service.create_document.return_value = created_doc
        
        doc_data = {
            "title": "Test Document",
            "content": "Test content",
            "file_type": "txt",
            "size": 100
        }
        
        response = client.post("/api/v1/documents/", json=doc_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Test Document"
        assert data["content"] == "Test content"
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_get_document(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试获取文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        doc_id = uuid.uuid4()
        document = Document(
            id=doc_id,
            title="Test Document",
            content="Test content",
            file_type="txt",
            size=100,
            owner_id=test_user.id
        )
        mock_service.get_document.return_value = document
        
        response = client.get(f"/api/v1/documents/{doc_id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Test Document"
        assert str(data["id"]) == str(doc_id)
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_get_document_not_found(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试获取不存在的文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        mock_service.get_document.return_value = None
        
        doc_id = uuid.uuid4()
        response = client.get(f"/api/v1/documents/{doc_id}", headers=auth_headers)
        assert response.status_code == 404
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_list_documents(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试列出文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        documents = [
            Document(
                id=uuid.uuid4(),
                title="Document 1",
                content="Content 1",
                file_type="txt",
                size=100,
                owner_id=test_user.id
            ),
            Document(
                id=uuid.uuid4(),
                title="Document 2",
                content="Content 2",
                file_type="txt",
                size=200,
                owner_id=test_user.id
            )
        ]
        mock_service.list_documents.return_value = documents
        
        response = client.get("/api/v1/documents/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["title"] == "Document 1"
        assert data[1]["title"] == "Document 2"
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_update_document(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试更新文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        doc_id = uuid.uuid4()
        updated_doc = Document(
            id=doc_id,
            title="Updated Document",
            content="Updated content",
            file_type="txt",
            size=150,
            owner_id=test_user.id
        )
        mock_service.update_document.return_value = updated_doc
        
        update_data = {
            "title": "Updated Document",
            "content": "Updated content"
        }
        
        response = client.put(f"/api/v1/documents/{doc_id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Updated Document"
        assert data["content"] == "Updated content"
    
    @patch('backend.app.api.deps.get_current_user')
    @patch('backend.app.api.deps.get_document_service')
    def test_delete_document(self, mock_get_service, mock_get_current_user, client, test_user, auth_headers):
        """测试删除文档"""
        mock_get_current_user.return_value = test_user
        
        # 模拟文档服务
        mock_service = Mock()
        mock_get_service.return_value = mock_service
        
        doc_id = uuid.uuid4()
        deleted_doc = Document(
            id=doc_id,
            title="Deleted Document",
            content="Deleted content",
            file_type="txt",
            size=100,
            owner_id=test_user.id
        )
        mock_service.delete_document.return_value = deleted_doc
        
        response = client.delete(f"/api/v1/documents/{doc_id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Deleted Document"


class TestErrorHandling:
    """错误处理测试"""
    
    def test_404_not_found(self, client):
        """测试404错误"""
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
    
    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        response = client.get("/api/v1/users/me")
        assert response.status_code == 401
    
    @patch('backend.app.api.deps.get_current_user')
    def test_validation_error(self, mock_get_current_user, client, test_user, auth_headers):
        """测试验证错误"""
        mock_get_current_user.return_value = test_user
        
        # 发送无效数据
        invalid_data = {
            "title": "",  # 空标题应该触发验证错误
            "content": "Test content",
            "file_type": "txt",
            "size": 100
        }
        
        response = client.post("/api/v1/documents/", json=invalid_data, headers=auth_headers)
        assert response.status_code == 422  # Validation error

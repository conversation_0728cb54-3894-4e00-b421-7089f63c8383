"""
Backend services 单元测试
"""

import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from sqlmodel import Session, create_engine
from sqlalchemy.pool import StaticPool

# 导入要测试的服务和模型
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend'))

from app.models import User, Document, DocumentCreate, DocumentChunk
from app.services.document import DocumentService, ChunkService, ProcessingService
from app.services.embedding import EmbeddingService
from engines.text_splitter.models import TextChunk, SplitResult
from engines.text_splitter.strategies import TokenBasedStrategy


@pytest.fixture
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture
def session(engine):
    """创建测试数据库会话"""
    from app.models.base import SQLModel
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


@pytest.fixture
def test_user(session):
    """创建测试用户"""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User"
    )
    session.add(user)
    session.commit()
    session.refresh(user)
    return user


@pytest.fixture
def test_document(session, test_user):
    """创建测试文档"""
    document = Document(
        title="Test Document",
        content="This is test content for splitting",
        file_type="txt",
        size=100,
        owner_id=test_user.id
    )
    session.add(document)
    session.commit()
    session.refresh(document)
    return document


class TestDocumentService:
    """文档服务测试"""
    
    def test_document_service_initialization(self, session):
        """测试文档服务初始化"""
        service = DocumentService(session)
        assert service.session == session
    
    def test_create_document(self, session, test_user):
        """测试创建文档"""
        service = DocumentService(session)
        
        doc_create = DocumentCreate(
            title="New Document",
            content="New content",
            file_type="txt",
            size=50
        )
        
        document = service.create_document(doc_create, test_user.id)
        
        assert document.title == "New Document"
        assert document.content == "New content"
        assert document.owner_id == test_user.id
    
    def test_get_document(self, session, test_document):
        """测试获取文档"""
        service = DocumentService(session)
        
        retrieved_doc = service.get_document(test_document.id)
        
        assert retrieved_doc is not None
        assert retrieved_doc.id == test_document.id
        assert retrieved_doc.title == test_document.title
    
    def test_get_document_not_found(self, session):
        """测试获取不存在的文档"""
        service = DocumentService(session)
        
        non_existent_id = uuid.uuid4()
        document = service.get_document(non_existent_id)
        
        assert document is None
    
    def test_list_documents(self, session, test_user, test_document):
        """测试列出文档"""
        service = DocumentService(session)
        
        documents = service.list_documents(test_user.id)
        
        assert len(documents) >= 1
        assert any(doc.id == test_document.id for doc in documents)
    
    def test_update_document(self, session, test_document):
        """测试更新文档"""
        service = DocumentService(session)
        
        updated_doc = service.update_document(
            test_document.id,
            title="Updated Title",
            content="Updated content"
        )
        
        assert updated_doc.title == "Updated Title"
        assert updated_doc.content == "Updated content"
        assert updated_doc.id == test_document.id
    
    def test_delete_document(self, session, test_document):
        """测试删除文档"""
        service = DocumentService(session)
        
        deleted_doc = service.delete_document(test_document.id)
        
        assert deleted_doc is not None
        assert deleted_doc.id == test_document.id
        
        # 验证文档已被删除
        retrieved_doc = service.get_document(test_document.id)
        assert retrieved_doc is None


class TestChunkService:
    """文档块服务测试"""
    
    def test_chunk_service_initialization(self, session):
        """测试文档块服务初始化"""
        service = ChunkService(session)
        assert service.session == session
    
    def test_create_chunk(self, session, test_document):
        """测试创建文档块"""
        service = ChunkService(session)
        
        chunk = service.create_chunk(
            document_id=test_document.id,
            content="Test chunk content",
            chunk_index=0,
            start_char=0,
            end_char=18,
            token_count=3
        )
        
        assert chunk.content == "Test chunk content"
        assert chunk.chunk_index == 0
        assert chunk.document_id == test_document.id
    
    def test_get_chunks_by_document(self, session, test_document):
        """测试获取文档的所有块"""
        service = ChunkService(session)
        
        # 先创建一些块
        service.create_chunk(
            document_id=test_document.id,
            content="Chunk 1",
            chunk_index=0,
            start_char=0,
            end_char=7,
            token_count=2
        )
        service.create_chunk(
            document_id=test_document.id,
            content="Chunk 2",
            chunk_index=1,
            start_char=7,
            end_char=14,
            token_count=2
        )
        
        chunks = service.get_chunks_by_document(test_document.id)
        
        assert len(chunks) == 2
        assert chunks[0].chunk_index == 0
        assert chunks[1].chunk_index == 1
    
    def test_delete_chunks_by_document(self, session, test_document):
        """测试删除文档的所有块"""
        service = ChunkService(session)
        
        # 先创建一些块
        service.create_chunk(
            document_id=test_document.id,
            content="Chunk 1",
            chunk_index=0,
            start_char=0,
            end_char=7,
            token_count=2
        )
        
        deleted_count = service.delete_chunks_by_document(test_document.id)
        
        assert deleted_count == 1
        
        # 验证块已被删除
        chunks = service.get_chunks_by_document(test_document.id)
        assert len(chunks) == 0


class TestProcessingService:
    """处理服务测试"""
    
    def test_processing_service_initialization(self, session):
        """测试处理服务初始化"""
        service = ProcessingService(session)
        assert service.session == session
    
    @patch('app.services.document.processing_service.TextSplitterEngine')
    def test_process_document(self, mock_engine_class, session, test_document):
        """测试处理文档"""
        # 模拟文本分割引擎
        mock_engine = Mock()
        mock_engine_class.return_value = mock_engine
        
        # 模拟分割结果
        mock_chunks = [
            TextChunk(
                content="First chunk",
                chunk_index=0,
                start_char=0,
                end_char=11,
                token_count=2
            ),
            TextChunk(
                content="Second chunk",
                chunk_index=1,
                start_char=11,
                end_char=23,
                token_count=2
            )
        ]
        
        mock_result = SplitResult(
            document_id=str(test_document.id),
            chunks=mock_chunks,
            strategy_used="token",
            total_chunks=2
        )
        
        mock_engine.split_document.return_value = mock_result
        
        service = ProcessingService(session)
        result = service.process_document(test_document.id)
        
        assert result is not None
        assert result.total_chunks == 2
        assert result.strategy_used == "token"
    
    @patch('app.services.document.processing_service.TextSplitterEngine')
    def test_process_document_with_strategy(self, mock_engine_class, session, test_document):
        """测试使用指定策略处理文档"""
        # 模拟文本分割引擎
        mock_engine = Mock()
        mock_engine_class.return_value = mock_engine
        
        # 模拟分割结果
        mock_chunks = [
            TextChunk(
                content="Chunk content",
                chunk_index=0,
                start_char=0,
                end_char=13,
                token_count=2
            )
        ]
        
        mock_result = SplitResult(
            document_id=str(test_document.id),
            chunks=mock_chunks,
            strategy_used="character",
            total_chunks=1
        )
        
        mock_engine.split_document.return_value = mock_result
        
        service = ProcessingService(session)
        strategy = TokenBasedStrategy(max_tokens=100)
        result = service.process_document_with_strategy(test_document.id, strategy)
        
        assert result is not None
        assert result.total_chunks == 1
    
    def test_get_processing_stats(self, session, test_document):
        """测试获取处理统计"""
        service = ProcessingService(session)
        
        # 先创建一些块来模拟处理结果
        chunk_service = ChunkService(session)
        chunk_service.create_chunk(
            document_id=test_document.id,
            content="Test chunk",
            chunk_index=0,
            start_char=0,
            end_char=10,
            token_count=2
        )
        
        stats = service.get_processing_stats(test_document.id)
        
        assert stats is not None
        assert "total_chunks" in stats
        assert stats["total_chunks"] >= 1


class TestEmbeddingService:
    """嵌入服务测试"""
    
    @pytest.mark.asyncio
    async def test_embedding_service_initialization(self):
        """测试嵌入服务初始化"""
        service = EmbeddingService()
        assert service is not None
    
    @pytest.mark.asyncio
    @patch('backend.app.services.embedding.openai.AsyncOpenAI')
    async def test_get_embedding(self, mock_openai_class):
        """测试获取嵌入向量"""
        # 模拟OpenAI客户端
        mock_client = AsyncMock()
        mock_openai_class.return_value = mock_client
        
        # 模拟嵌入响应
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1, 0.2, 0.3])]
        mock_client.embeddings.create.return_value = mock_response
        
        service = EmbeddingService()
        embedding = await service.get_embedding("test text")
        
        assert embedding == [0.1, 0.2, 0.3]
        mock_client.embeddings.create.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('backend.app.services.embedding.openai.AsyncOpenAI')
    async def test_get_embeddings_batch(self, mock_openai_class):
        """测试批量获取嵌入向量"""
        # 模拟OpenAI客户端
        mock_client = AsyncMock()
        mock_openai_class.return_value = mock_client
        
        # 模拟嵌入响应
        mock_response = Mock()
        mock_response.data = [
            Mock(embedding=[0.1, 0.2, 0.3]),
            Mock(embedding=[0.4, 0.5, 0.6])
        ]
        mock_client.embeddings.create.return_value = mock_response
        
        service = EmbeddingService()
        embeddings = await service.get_embeddings_batch(["text1", "text2"])
        
        assert len(embeddings) == 2
        assert embeddings[0] == [0.1, 0.2, 0.3]
        assert embeddings[1] == [0.4, 0.5, 0.6]
    
    @pytest.mark.asyncio
    @patch('backend.app.services.embedding.openai.AsyncOpenAI')
    async def test_get_embedding_error_handling(self, mock_openai_class):
        """测试嵌入服务错误处理"""
        # 模拟OpenAI客户端抛出异常
        mock_client = AsyncMock()
        mock_openai_class.return_value = mock_client
        mock_client.embeddings.create.side_effect = Exception("API Error")
        
        service = EmbeddingService()
        
        with pytest.raises(Exception):
            await service.get_embedding("test text")

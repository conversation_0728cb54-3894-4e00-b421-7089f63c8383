"""
Text-Splitter Models 单元测试
测试数据模型的验证器和方法
"""

import pytest
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.models import TextChunk, Document, SplitResult, ChunkType


class TestTextChunk:
    """TextChunk 模型测试"""
    
    def test_valid_text_chunk_creation(self):
        """测试正常的文本块创建"""
        chunk = TextChunk(
            content="这是一个测试文本块",
            chunk_index=0,
            start_char=0,
            end_char=9,
            token_count=5
        )
        
        assert chunk.content == "这是一个测试文本块"
        assert chunk.chunk_index == 0
        assert chunk.start_char == 0
        assert chunk.end_char == 9
        assert chunk.token_count == 5
        assert chunk.chunk_type == ChunkType.TEXT
    
    def test_content_validation(self):
        """测试内容验证"""
        # 空内容应该失败
        with pytest.raises(ValueError, match="Content cannot be empty"):
            TextChunk(
                content="",
                chunk_index=0,
                start_char=0,
                end_char=0
            )
        
        # 只有空白字符的内容应该失败
        with pytest.raises(ValueError, match="Content cannot be empty"):
            TextChunk(
                content="   \n\t  ",
                chunk_index=0,
                start_char=0,
                end_char=7
            )
    
    def test_position_validation(self):
        """测试位置验证"""
        # 负数起始位置应该失败
        with pytest.raises(ValueError, match="Character positions must be non-negative"):
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=-1,
                end_char=4
            )
        
        # 负数结束位置应该失败
        with pytest.raises(ValueError, match="Character positions must be non-negative"):
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=0,
                end_char=-1
            )
    
    def test_token_count_validation(self):
        """测试 token 数量验证"""
        # 负数 token 数量应该失败
        with pytest.raises(ValueError, match="Token count must be non-negative"):
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=0,
                end_char=4,
                token_count=-1
            )
        
        # None 应该被接受
        chunk = TextChunk(
            content="test",
            chunk_index=0,
            start_char=0,
            end_char=4,
            token_count=None
        )
        assert chunk.token_count is None
    
    def test_chunk_index_validation(self):
        """测试块索引验证"""
        # 负数索引应该失败
        with pytest.raises(ValueError, match="Chunk index must be non-negative"):
            TextChunk(
                content="test",
                chunk_index=-1,
                start_char=0,
                end_char=4
            )
    
    def test_position_consistency_validation(self):
        """测试位置一致性验证"""
        # end_char <= start_char 应该失败
        with pytest.raises(ValueError, match="end_char must be greater than start_char"):
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=5,
                end_char=4
            )
        
        # end_char == start_char 应该失败
        with pytest.raises(ValueError, match="end_char must be greater than start_char"):
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=5,
                end_char=5
            )
    
    def test_content_length_consistency(self):
        """测试内容长度一致性验证"""
        # 内容长度与位置差异过大应该失败
        with pytest.raises(ValueError, match="Content length .* does not match calculated length"):
            TextChunk(
                content="short",  # 5 字符
                chunk_index=0,
                start_char=0,
                end_char=100  # 声称 100 字符
            )
    
    def test_get_length_method(self):
        """测试获取长度方法"""
        chunk = TextChunk(
            content="test content",
            chunk_index=0,
            start_char=10,
            end_char=22
        )
        assert chunk.get_length() == 12
    
    def test_get_preview_method(self):
        """测试获取预览方法"""
        chunk = TextChunk(
            content="这是一个很长的测试内容，用来测试预览功能是否正常工作",
            chunk_index=0,
            start_char=0,
            end_char=26
        )
        
        # 短预览
        preview = chunk.get_preview(10)
        assert len(preview) <= 13  # 10 + "..."
        assert preview.endswith("...")
        
        # 长预览（不需要截断）
        short_chunk = TextChunk(
            content="短内容",
            chunk_index=0,
            start_char=0,
            end_char=3
        )
        preview = short_chunk.get_preview(100)
        assert preview == "短内容"
        assert not preview.endswith("...")


class TestDocument:
    """Document 模型测试"""
    
    def test_valid_document_creation(self):
        """测试正常的文档创建"""
        content = "这是一个测试文档内容"
        doc = Document(
            title="测试文档",
            content=content,
            file_type="txt",
            size=len(content.encode('utf-8'))
        )
        
        assert doc.title == "测试文档"
        assert doc.content == content
        assert doc.file_type == "txt"
        assert doc.size == len(content.encode('utf-8'))
        assert doc.encoding == "utf-8"
        assert doc.id is not None
        assert isinstance(doc.created_at, datetime)
    
    def test_title_validation(self):
        """测试标题验证"""
        # 空标题应该失败
        with pytest.raises(ValueError, match="Document title cannot be empty"):
            Document(
                title="",
                content="test content",
                file_type="txt",
                size=12
            )
        
        # 只有空白字符的标题应该失败
        with pytest.raises(ValueError, match="Document title cannot be empty"):
            Document(
                title="   \n\t  ",
                content="test content",
                file_type="txt",
                size=12
            )
        
        # 标题应该被自动去除空白
        doc = Document(
            title="  测试标题  ",
            content="test content",
            file_type="txt",
            size=12
        )
        assert doc.title == "测试标题"
    
    def test_content_validation(self):
        """测试内容验证"""
        # 空内容应该失败
        with pytest.raises(ValueError, match="Document content cannot be empty"):
            Document(
                title="Test",
                content="",
                file_type="txt",
                size=0
            )
        
        # 只有空白字符的内容应该失败
        with pytest.raises(ValueError, match="Document content cannot be empty"):
            Document(
                title="Test",
                content="   \n\t  ",
                file_type="txt",
                size=7
            )
    
    def test_file_type_validation(self):
        """测试文件类型验证"""
        # 空文件类型应该失败
        with pytest.raises(ValueError, match="File type cannot be empty"):
            Document(
                title="Test",
                content="test content",
                file_type="",
                size=12
            )
        
        # 文件类型应该被转换为小写并去除空白
        doc = Document(
            title="Test",
            content="test content",
            file_type="  TXT  ",
            size=12
        )
        assert doc.file_type == "txt"
    
    def test_size_validation(self):
        """测试大小验证"""
        # 负数大小应该失败
        with pytest.raises(ValueError, match="Size must be non-negative"):
            Document(
                title="Test",
                content="test content",
                file_type="txt",
                size=-1
            )
    
    def test_size_content_consistency_warning(self, caplog):
        """测试大小与内容一致性（应该只警告）"""
        import logging
        
        # 设置日志级别
        caplog.set_level(logging.WARNING)
        
        # 创建大小不匹配的文档（应该成功但有警告）
        doc = Document(
            title="Test",
            content="test content",
            file_type="txt",
            size=1000  # 明显不匹配
        )
        
        assert doc.size == 1000  # 文档应该创建成功
        # 检查是否有警告日志
        assert any("Size mismatch" in record.message for record in caplog.records)
    
    def test_utility_methods(self):
        """测试工具方法"""
        doc = Document(
            title="Test Document",
            content="This is a test document with multiple words.",
            file_type="txt",
            size=44
        )

        # 测试单词计数
        assert doc.get_word_count() == 8  # "This is a test document with multiple words." = 8 words

        # 测试字符计数
        assert doc.get_char_count() == 44

        # 测试支持的类型检查
        assert doc.is_supported_type() == True


class TestSplitResult:
    """SplitResult 模型测试"""
    
    def test_valid_split_result_creation(self):
        """测试正常的分割结果创建"""
        chunks = [
            TextChunk(
                content="第一块",
                chunk_index=0,
                start_char=0,
                end_char=3
            ),
            TextChunk(
                content="第二块",
                chunk_index=1,
                start_char=3,
                end_char=6
            )
        ]
        
        result = SplitResult(
            document_id="test-doc-id",
            chunks=chunks,
            strategy_used="token_based",
            total_chunks=2
        )
        
        assert result.document_id == "test-doc-id"
        assert len(result.chunks) == 2
        assert result.strategy_used == "token_based"
        assert result.total_chunks == 2
        assert result.error is None
        assert isinstance(result.created_at, datetime)

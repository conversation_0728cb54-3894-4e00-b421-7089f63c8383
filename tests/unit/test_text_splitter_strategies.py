"""
Text-Splitter Strategies 单元测试
测试各种分割策略的功能
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.strategies import (
    SplitStrategy, 
    TokenBasedStrategy, 
    CharacterBasedStrategy, 
    MarkdownStrategy
)


class TestTokenBasedStrategy:
    """TokenBasedStrategy 测试"""
    
    def test_default_creation(self):
        """测试默认创建"""
        strategy = TokenBasedStrategy()
        
        assert strategy.name == "token_based"
        assert strategy.max_tokens == 1000
        assert strategy.model_name == "gpt-3.5-turbo"
    
    def test_custom_parameters(self):
        """测试自定义参数"""
        strategy = TokenBasedStrategy(
            max_tokens=500,
            model_name="gpt-4"
        )
        
        assert strategy.max_tokens == 500
        assert strategy.model_name == "gpt-4"
    
    def test_get_params(self):
        """测试获取参数"""
        strategy = TokenBasedStrategy(
            max_tokens=800,
            model_name="gpt-3.5-turbo-16k"
        )
        
        params = strategy.get_params()
        expected = {
            "max_tokens": 800,
            "model_name": "gpt-3.5-turbo-16k"
        }
        
        assert params == expected
    
    def test_validation(self):
        """测试参数验证"""
        # 正常情况
        strategy = TokenBasedStrategy(max_tokens=100, model_name="gpt-4")
        assert strategy.max_tokens == 100
        
        # 测试边界值
        strategy = TokenBasedStrategy(max_tokens=1)
        assert strategy.max_tokens == 1


class TestCharacterBasedStrategy:
    """CharacterBasedStrategy 测试"""
    
    def test_default_creation(self):
        """测试默认创建"""
        strategy = CharacterBasedStrategy()
        
        assert strategy.name == "character_based"
        assert strategy.max_chars == 2000
    
    def test_custom_parameters(self):
        """测试自定义参数"""
        strategy = CharacterBasedStrategy(max_chars=1500)
        
        assert strategy.max_chars == 1500
    
    def test_get_params(self):
        """测试获取参数"""
        strategy = CharacterBasedStrategy(max_chars=1200)
        
        params = strategy.get_params()
        expected = {"max_chars": 1200}
        
        assert params == expected
    
    def test_validation(self):
        """测试参数验证"""
        # 正常情况
        strategy = CharacterBasedStrategy(max_chars=500)
        assert strategy.max_chars == 500
        
        # 测试边界值
        strategy = CharacterBasedStrategy(max_chars=1)
        assert strategy.max_chars == 1


class TestMarkdownStrategy:
    """MarkdownStrategy 测试"""
    
    def test_default_creation(self):
        """测试默认创建"""
        strategy = MarkdownStrategy()
        
        assert strategy.name == "markdown"
        assert strategy.max_chars == 1500
        assert strategy.preserve_headers == True
    
    def test_custom_parameters(self):
        """测试自定义参数"""
        strategy = MarkdownStrategy(
            max_chars=1000,
            preserve_headers=False
        )
        
        assert strategy.max_chars == 1000
        assert strategy.preserve_headers == False
    
    def test_get_params(self):
        """测试获取参数"""
        strategy = MarkdownStrategy(
            max_chars=800,
            preserve_headers=True
        )
        
        params = strategy.get_params()
        expected = {
            "max_chars": 800,
            "preserve_headers": True
        }
        
        assert params == expected


class TestSplitStrategyInterface:
    """SplitStrategy 接口测试"""
    
    def test_abstract_base_class(self):
        """测试抽象基类"""
        # 不能直接实例化抽象基类
        with pytest.raises(TypeError):
            SplitStrategy(name="test")
    
    def test_strategy_inheritance(self):
        """测试策略继承"""
        # 所有策略都应该继承自 SplitStrategy
        assert issubclass(TokenBasedStrategy, SplitStrategy)
        assert issubclass(CharacterBasedStrategy, SplitStrategy)
        assert issubclass(MarkdownStrategy, SplitStrategy)
    
    def test_required_methods(self):
        """测试必需的方法"""
        strategies = [
            TokenBasedStrategy(),
            CharacterBasedStrategy(),
            MarkdownStrategy()
        ]
        
        for strategy in strategies:
            # 每个策略都应该有 get_params 方法
            assert hasattr(strategy, 'get_params')
            assert callable(strategy.get_params)
            
            # get_params 应该返回字典
            params = strategy.get_params()
            assert isinstance(params, dict)
            
            # 每个策略都应该有 name 属性
            assert hasattr(strategy, 'name')
            assert isinstance(strategy.name, str)
            assert len(strategy.name) > 0


class TestStrategyComparison:
    """策略比较测试"""
    
    def test_strategy_equality(self):
        """测试策略相等性"""
        # 相同参数的策略应该相等
        strategy1 = TokenBasedStrategy(max_tokens=500, model_name="gpt-4")
        strategy2 = TokenBasedStrategy(max_tokens=500, model_name="gpt-4")
        
        assert strategy1.max_tokens == strategy2.max_tokens
        assert strategy1.model_name == strategy2.model_name
        assert strategy1.name == strategy2.name
    
    def test_strategy_difference(self):
        """测试策略差异"""
        # 不同参数的策略应该不同
        strategy1 = TokenBasedStrategy(max_tokens=500)
        strategy2 = TokenBasedStrategy(max_tokens=1000)
        
        assert strategy1.max_tokens != strategy2.max_tokens
        
        # 不同类型的策略应该不同
        token_strategy = TokenBasedStrategy()
        char_strategy = CharacterBasedStrategy()
        
        assert token_strategy.name != char_strategy.name


class TestStrategyParameterValidation:
    """策略参数验证测试"""
    
    def test_token_strategy_parameter_bounds(self):
        """测试 Token 策略参数边界"""
        # 测试极小值
        strategy = TokenBasedStrategy(max_tokens=1)
        assert strategy.max_tokens == 1
        
        # 测试极大值
        strategy = TokenBasedStrategy(max_tokens=100000)
        assert strategy.max_tokens == 100000
        
        # 测试模型名称
        strategy = TokenBasedStrategy(model_name="custom-model")
        assert strategy.model_name == "custom-model"
    
    def test_character_strategy_parameter_bounds(self):
        """测试字符策略参数边界"""
        # 测试极小值
        strategy = CharacterBasedStrategy(max_chars=1)
        assert strategy.max_chars == 1
        
        # 测试极大值
        strategy = CharacterBasedStrategy(max_chars=1000000)
        assert strategy.max_chars == 1000000
    
    def test_markdown_strategy_parameter_bounds(self):
        """测试 Markdown 策略参数边界"""
        # 测试极小值
        strategy = MarkdownStrategy(max_chars=1)
        assert strategy.max_chars == 1
        
        # 测试布尔参数
        strategy = MarkdownStrategy(preserve_headers=False)
        assert strategy.preserve_headers == False
        
        strategy = MarkdownStrategy(preserve_headers=True)
        assert strategy.preserve_headers == True


class TestStrategyUsagePatterns:
    """策略使用模式测试"""
    
    def test_common_token_configurations(self):
        """测试常见的 Token 配置"""
        # GPT-3.5 配置
        gpt35_strategy = TokenBasedStrategy(
            max_tokens=4000,
            model_name="gpt-3.5-turbo"
        )
        assert gpt35_strategy.max_tokens == 4000
        
        # GPT-4 配置
        gpt4_strategy = TokenBasedStrategy(
            max_tokens=8000,
            model_name="gpt-4"
        )
        assert gpt4_strategy.max_tokens == 8000
    
    def test_common_character_configurations(self):
        """测试常见的字符配置"""
        # 短文本配置
        short_strategy = CharacterBasedStrategy(max_chars=500)
        assert short_strategy.max_chars == 500
        
        # 长文本配置
        long_strategy = CharacterBasedStrategy(max_chars=5000)
        assert long_strategy.max_chars == 5000
    
    def test_common_markdown_configurations(self):
        """测试常见的 Markdown 配置"""
        # 保留标题的配置
        preserve_strategy = MarkdownStrategy(
            max_chars=2000,
            preserve_headers=True
        )
        assert preserve_strategy.preserve_headers == True
        
        # 不保留标题的配置
        no_preserve_strategy = MarkdownStrategy(
            max_chars=1500,
            preserve_headers=False
        )
        assert no_preserve_strategy.preserve_headers == False

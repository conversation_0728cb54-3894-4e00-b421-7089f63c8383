"""
Backend CRUD 操作单元测试
"""

import pytest
import uuid
from datetime import datetime
from sqlmodel import Session, create_engine
from sqlalchemy.pool import StaticPool

# 导入要测试的模型和CRUD
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend'))

from app.models import (
    User, UserCreate, UserUpdate,
    Document, DocumentCreate, DocumentUpdate,
    Topic, TopicCreate, TopicUpdate,
    Item, ItemCreate, ItemUpdate
)
from app import crud


@pytest.fixture
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture
def session(engine):
    """创建测试数据库会话"""
    from app.models.base import SQLModel
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


@pytest.fixture
def test_user(session):
    """创建测试用户"""
    user_create = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    user = crud.create_user(session=session, user_create=user_create)
    return user


class TestUserCRUD:
    """用户CRUD测试"""
    
    def test_create_user(self, session):
        """测试创建用户"""
        user_create = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="New User"
        )
        user = crud.create_user(session=session, user_create=user_create)
        
        assert user.email == "<EMAIL>"
        assert user.full_name == "New User"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.hashed_password != "password123"  # 应该被哈希
    
    def test_get_user_by_email(self, session, test_user):
        """测试通过邮箱获取用户"""
        user = crud.get_user_by_email(session=session, email=test_user.email)
        
        assert user is not None
        assert user.email == test_user.email
        assert user.id == test_user.id
    
    def test_get_user_by_email_not_found(self, session):
        """测试获取不存在的用户"""
        user = crud.get_user_by_email(session=session, email="<EMAIL>")
        assert user is None
    
    def test_authenticate_user(self, session, test_user):
        """测试用户认证"""
        # 正确密码
        authenticated_user = crud.authenticate(
            session=session,
            email=test_user.email,
            password="testpassword123"
        )
        assert authenticated_user is not None
        assert authenticated_user.id == test_user.id
        
        # 错误密码
        failed_auth = crud.authenticate(
            session=session,
            email=test_user.email,
            password="wrongpassword"
        )
        assert failed_auth is False
    
    def test_update_user(self, session, test_user):
        """测试更新用户"""
        user_update = UserUpdate(
            full_name="Updated User",
            email="<EMAIL>"
        )
        updated_user = crud.update_user(
            session=session,
            db_user=test_user,
            user_update=user_update
        )
        
        assert updated_user.full_name == "Updated User"
        assert updated_user.email == "<EMAIL>"
        assert updated_user.id == test_user.id


class TestDocumentCRUD:
    """文档CRUD测试"""
    
    def test_create_document(self, session, test_user):
        """测试创建文档"""
        doc_create = DocumentCreate(
            title="Test Document",
            content="This is test content",
            file_type="txt",
            size=100
        )
        document = crud.create_document(
            session=session,
            document_create=doc_create,
            owner_id=test_user.id
        )
        
        assert document.title == "Test Document"
        assert document.content == "This is test content"
        assert document.file_type == "txt"
        assert document.size == 100
        assert document.owner_id == test_user.id
    
    def test_get_document(self, session, test_user):
        """测试获取文档"""
        # 先创建文档
        doc_create = DocumentCreate(
            title="Test Document",
            content="Test content",
            file_type="txt",
            size=100
        )
        created_doc = crud.create_document(
            session=session,
            document_create=doc_create,
            owner_id=test_user.id
        )
        
        # 获取文档
        retrieved_doc = crud.get_document(session=session, document_id=created_doc.id)
        
        assert retrieved_doc is not None
        assert retrieved_doc.id == created_doc.id
        assert retrieved_doc.title == "Test Document"
    
    def test_get_document_not_found(self, session):
        """测试获取不存在的文档"""
        non_existent_id = uuid.uuid4()
        document = crud.get_document(session=session, document_id=non_existent_id)
        assert document is None
    
    def test_update_document(self, session, test_user):
        """测试更新文档"""
        # 先创建文档
        doc_create = DocumentCreate(
            title="Original Title",
            content="Original content",
            file_type="txt",
            size=100
        )
        document = crud.create_document(
            session=session,
            document_create=doc_create,
            owner_id=test_user.id
        )
        
        # 更新文档
        doc_update = DocumentUpdate(
            title="Updated Title",
            content="Updated content"
        )
        updated_doc = crud.update_document(
            session=session,
            db_document=document,
            document_update=doc_update
        )
        
        assert updated_doc.title == "Updated Title"
        assert updated_doc.content == "Updated content"
        assert updated_doc.id == document.id
    
    def test_delete_document(self, session, test_user):
        """测试删除文档"""
        # 先创建文档
        doc_create = DocumentCreate(
            title="To Delete",
            content="Content to delete",
            file_type="txt",
            size=100
        )
        document = crud.create_document(
            session=session,
            document_create=doc_create,
            owner_id=test_user.id
        )
        
        # 删除文档
        deleted_doc = crud.delete_document(session=session, document_id=document.id)
        
        assert deleted_doc is not None
        assert deleted_doc.id == document.id
        
        # 验证文档已被删除
        retrieved_doc = crud.get_document(session=session, document_id=document.id)
        assert retrieved_doc is None


class TestTopicCRUD:
    """主题CRUD测试"""
    
    def test_create_topic(self, session, test_user):
        """测试创建主题"""
        topic_create = TopicCreate(
            name="Test Topic",
            description="This is a test topic"
        )
        topic = crud.create_topic(
            session=session,
            topic_create=topic_create,
            owner_id=test_user.id
        )
        
        assert topic.name == "Test Topic"
        assert topic.description == "This is a test topic"
        assert topic.owner_id == test_user.id
    
    def test_get_topic(self, session, test_user):
        """测试获取主题"""
        # 先创建主题
        topic_create = TopicCreate(
            name="Test Topic",
            description="Test description"
        )
        created_topic = crud.create_topic(
            session=session,
            topic_create=topic_create,
            owner_id=test_user.id
        )
        
        # 获取主题
        retrieved_topic = crud.get_topic(session=session, topic_id=created_topic.id)
        
        assert retrieved_topic is not None
        assert retrieved_topic.id == created_topic.id
        assert retrieved_topic.name == "Test Topic"
    
    def test_update_topic(self, session, test_user):
        """测试更新主题"""
        # 先创建主题
        topic_create = TopicCreate(
            name="Original Topic",
            description="Original description"
        )
        topic = crud.create_topic(
            session=session,
            topic_create=topic_create,
            owner_id=test_user.id
        )
        
        # 更新主题
        topic_update = TopicUpdate(
            name="Updated Topic",
            description="Updated description"
        )
        updated_topic = crud.update_topic(
            session=session,
            db_topic=topic,
            topic_update=topic_update
        )
        
        assert updated_topic.name == "Updated Topic"
        assert updated_topic.description == "Updated description"
        assert updated_topic.id == topic.id
    
    def test_delete_topic(self, session, test_user):
        """测试删除主题"""
        # 先创建主题
        topic_create = TopicCreate(
            name="To Delete",
            description="Topic to delete"
        )
        topic = crud.create_topic(
            session=session,
            topic_create=topic_create,
            owner_id=test_user.id
        )
        
        # 删除主题
        deleted_topic = crud.delete_topic(session=session, topic_id=topic.id)
        
        assert deleted_topic is not None
        assert deleted_topic.id == topic.id
        
        # 验证主题已被删除
        retrieved_topic = crud.get_topic(session=session, topic_id=topic.id)
        assert retrieved_topic is None


class TestItemCRUD:
    """项目CRUD测试"""
    
    def test_create_item(self, session, test_user):
        """测试创建项目"""
        item_create = ItemCreate(
            title="Test Item",
            description="This is a test item"
        )
        item = crud.create_item(
            session=session,
            item_create=item_create,
            owner_id=test_user.id
        )
        
        assert item.title == "Test Item"
        assert item.description == "This is a test item"
        assert item.owner_id == test_user.id
    
    def test_get_item(self, session, test_user):
        """测试获取项目"""
        # 先创建项目
        item_create = ItemCreate(
            title="Test Item",
            description="Test description"
        )
        created_item = crud.create_item(
            session=session,
            item_create=item_create,
            owner_id=test_user.id
        )
        
        # 获取项目
        retrieved_item = crud.get_item(session=session, item_id=created_item.id)
        
        assert retrieved_item is not None
        assert retrieved_item.id == created_item.id
        assert retrieved_item.title == "Test Item"
    
    def test_update_item(self, session, test_user):
        """测试更新项目"""
        # 先创建项目
        item_create = ItemCreate(
            title="Original Item",
            description="Original description"
        )
        item = crud.create_item(
            session=session,
            item_create=item_create,
            owner_id=test_user.id
        )
        
        # 更新项目
        item_update = ItemUpdate(
            title="Updated Item",
            description="Updated description"
        )
        updated_item = crud.update_item(
            session=session,
            db_item=item,
            item_update=item_update
        )
        
        assert updated_item.title == "Updated Item"
        assert updated_item.description == "Updated description"
        assert updated_item.id == item.id
    
    def test_delete_item(self, session, test_user):
        """测试删除项目"""
        # 先创建项目
        item_create = ItemCreate(
            title="To Delete",
            description="Item to delete"
        )
        item = crud.create_item(
            session=session,
            item_create=item_create,
            owner_id=test_user.id
        )
        
        # 删除项目
        deleted_item = crud.delete_item(session=session, item_id=item.id)
        
        assert deleted_item is not None
        assert deleted_item.id == item.id
        
        # 验证项目已被删除
        retrieved_item = crud.get_item(session=session, item_id=item.id)
        assert retrieved_item is None

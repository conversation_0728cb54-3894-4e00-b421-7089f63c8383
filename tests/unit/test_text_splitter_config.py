"""
Text-Splitter Config 单元测试
测试配置类的功能
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.config import TextSplitterConfig


class TestTextSplitterConfig:
    """TextSplitterConfig 测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = TextSplitterConfig()

        # 验证默认值
        assert config.default_model == "gpt-3.5-turbo"
        assert config.default_max_tokens == 1000
        assert config.default_max_chars == 2000
        assert config.markdown_max_chars == 1500
        assert config.enable_caching == True
        assert config.batch_size == 10
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = TextSplitterConfig(
            default_model="gpt-4",
            default_max_tokens=2000,
            default_max_chars=4000,
            markdown_max_chars=3000,
            enable_caching=False,
            batch_size=50
        )

        assert config.default_model == "gpt-4"
        assert config.default_max_tokens == 2000
        assert config.default_max_chars == 4000
        assert config.markdown_max_chars == 3000
        assert config.enable_caching == False
        assert config.batch_size == 50
    
    def test_partial_custom_config(self):
        """测试部分自定义配置"""
        config = TextSplitterConfig(
            default_max_tokens=1500,
            markdown_max_chars=2000
        )
        
        # 自定义的值
        assert config.default_max_tokens == 1500
        assert config.markdown_max_chars == 2000
        
        # 默认值应该保持不变
        assert config.default_model == "gpt-3.5-turbo"
        assert config.default_max_chars == 2000
        assert config.enable_caching == True
        assert config.batch_size == 10
    
    def test_config_validation(self):
        """测试配置验证"""
        # 正常配置应该通过
        config = TextSplitterConfig(
            default_max_tokens=500,
            default_max_chars=1000
        )
        assert config.default_max_tokens == 500
        assert config.default_max_chars == 1000
    
    def test_config_immutability(self):
        """测试配置不可变性"""
        config = TextSplitterConfig()
        
        # 尝试修改配置（如果配置是不可变的，这应该失败）
        # 注意：这取决于具体的实现
        original_model = config.default_model
        
        # 验证配置值
        assert config.default_model == original_model
    
    def test_config_serialization(self):
        """测试配置序列化"""
        config = TextSplitterConfig(
            default_model="gpt-4",
            default_max_tokens=1500
        )
        
        # 测试转换为字典
        config_dict = config.model_dump()
        
        assert isinstance(config_dict, dict)
        assert config_dict["default_model"] == "gpt-4"
        assert config_dict["default_max_tokens"] == 1500
    
    def test_config_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            "default_model": "gpt-4",
            "default_max_tokens": 1200,
            "default_max_chars": 2400,
            "markdown_max_chars": 1800,
            "enable_caching": False,
            "batch_size": 75
        }
        
        config = TextSplitterConfig(**config_dict)
        
        assert config.default_model == "gpt-4"
        assert config.default_max_tokens == 1200
        assert config.default_max_chars == 2400
        assert config.markdown_max_chars == 1800
        assert config.enable_caching == False
        assert config.batch_size == 75
    
    def test_config_equality(self):
        """测试配置相等性"""
        config1 = TextSplitterConfig(
            default_model="gpt-3.5-turbo",
            default_max_tokens=1000
        )
        
        config2 = TextSplitterConfig(
            default_model="gpt-3.5-turbo",
            default_max_tokens=1000
        )
        
        config3 = TextSplitterConfig(
            default_model="gpt-4",
            default_max_tokens=1000
        )
        
        # 相同配置应该相等
        assert config1.default_model == config2.default_model
        assert config1.default_max_tokens == config2.default_max_tokens
        
        # 不同配置应该不相等
        assert config1.default_model != config3.default_model
    
    def test_config_copy(self):
        """测试配置复制"""
        original_config = TextSplitterConfig(
            default_model="gpt-4",
            default_max_tokens=1500
        )
        
        # 创建副本
        copied_config = TextSplitterConfig(
            **original_config.model_dump()
        )
        
        # 验证副本与原始配置相同
        assert copied_config.default_model == original_config.default_model
        assert copied_config.default_max_tokens == original_config.default_max_tokens
    
    def test_config_update(self):
        """测试配置更新"""
        config = TextSplitterConfig()
        
        # 创建更新后的配置
        updated_config = TextSplitterConfig(
            **{**config.model_dump(), "default_max_tokens": 2000}
        )
        
        # 验证更新
        assert updated_config.default_max_tokens == 2000
        assert updated_config.default_model == config.default_model  # 其他值保持不变


class TestTextSplitterConfigEdgeCases:
    """TextSplitterConfig 边界情况测试"""
    
    def test_extreme_values(self):
        """测试极值"""
        # 测试极小值
        config = TextSplitterConfig(
            default_max_tokens=1,
            default_max_chars=1,
            markdown_max_chars=1,
            batch_size=1
        )

        assert config.default_max_tokens == 1
        assert config.default_max_chars == 1
        assert config.markdown_max_chars == 1
        assert config.batch_size == 1

        # 测试极大值
        config = TextSplitterConfig(
            default_max_tokens=100000,
            default_max_chars=1000000,
            markdown_max_chars=500000,
            batch_size=10000
        )

        assert config.default_max_tokens == 100000
        assert config.default_max_chars == 1000000
        assert config.markdown_max_chars == 500000
        assert config.batch_size == 10000
    
    def test_boolean_values(self):
        """测试布尔值"""
        # 测试 True
        config_true = TextSplitterConfig(enable_caching=True)
        assert config_true.enable_caching == True
        
        # 测试 False
        config_false = TextSplitterConfig(enable_caching=False)
        assert config_false.enable_caching == False
    
    def test_string_values(self):
        """测试字符串值"""
        # 测试各种模型名称
        model_names = [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "custom-model",
            "text-davinci-003"
        ]
        
        for model_name in model_names:
            config = TextSplitterConfig(default_model=model_name)
            assert config.default_model == model_name
    
    def test_config_consistency(self):
        """测试配置一致性"""
        config = TextSplitterConfig(
            default_max_chars=1000,
            markdown_max_chars=1500
        )
        
        # Markdown 最大字符数可以大于默认最大字符数
        assert config.markdown_max_chars > config.default_max_chars
        
        # 或者相等
        config2 = TextSplitterConfig(
            default_max_chars=1500,
            markdown_max_chars=1500
        )
        assert config2.markdown_max_chars == config2.default_max_chars


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

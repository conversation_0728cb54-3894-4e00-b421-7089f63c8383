"""
Text-Splitter Engine 全面测试
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy, MarkdownStrategy
from engines.text_splitter.models import Document, TextChunk
from engines.text_splitter.config import TextSplitterConfig


class TestTextSplitterEngine:
    """TextSplitterEngine 全面测试"""

    def setup_method(self):
        """测试前设置"""
        self.engine = TextSplitterEngine()
        self.sample_text = "这是一个测试文档。" * 100  # 创建较长的测试文本
        self.short_text = "这是短文本。"
        self.english_text = "This is a test document. " * 50
        self.mixed_text = "这是中英文混合的文档。This is mixed content. " * 30

    def test_engine_initialization(self):
        """测试引擎初始化"""
        assert self.engine is not None
        assert self.engine.config is not None
        assert isinstance(self.engine.config, TextSplitterConfig)

        # 测试统计信息
        stats = self.engine.get_stats()
        assert isinstance(stats, dict)
        assert "splitters_loaded" in stats
        assert stats["splitters_loaded"] > 0

    def test_engine_initialization_with_custom_config(self):
        """测试使用自定义配置初始化引擎"""
        custom_config = TextSplitterConfig(
            default_max_tokens=500,
            default_max_chars=1500
        )
        engine = TextSplitterEngine(config=custom_config)

        assert engine.config.default_max_tokens == 500
        assert engine.config.default_max_chars == 1500

    def test_token_based_splitting(self):
        """测试基于 Token 的分割"""
        strategy = TokenBasedStrategy(max_tokens=50)
        chunks = self.engine.split_text(self.sample_text, strategy)

        assert len(chunks) > 1
        assert all(isinstance(chunk, TextChunk) for chunk in chunks)
        assert all(chunk.content for chunk in chunks)  # 确保内容不为空
        assert all(chunk.token_count is not None for chunk in chunks)
        assert all(chunk.token_count > 0 for chunk in chunks)

        # 验证块索引连续性
        for i, chunk in enumerate(chunks):
            assert chunk.chunk_index == i

        # 验证字符位置连续性
        for i in range(len(chunks) - 1):
            assert chunks[i].end_char <= chunks[i + 1].start_char

    def test_character_based_splitting(self):
        """测试基于字符的分割"""
        strategy = CharacterBasedStrategy(max_chars=100)
        chunks = self.engine.split_text(self.sample_text, strategy)

        assert len(chunks) > 1
        assert all(len(chunk.content) <= 120 for chunk in chunks)  # 允许一些误差

        # 验证字符位置准确性
        for chunk in chunks:
            expected_length = chunk.end_char - chunk.start_char
            actual_length = len(chunk.content)
            # 允许一些误差
            assert abs(expected_length - actual_length) <= max(10, actual_length * 0.1)

    def test_markdown_strategy_selection(self):
        """测试 Markdown 策略选择"""
        # 创建 Markdown 文档
        md_content = "# 标题\n\n这是内容。\n\n## 子标题\n\n更多内容。" * 20
        md_doc = Document(
            title="Markdown 文档",
            content=md_content,
            file_type="md",
            size=len(md_content.encode('utf-8'))
        )

        result = self.engine.split_document(md_doc)

        assert result.total_chunks > 0
        assert result.strategy_used == "character_based"  # Markdown 使用字符策略

    def test_document_splitting(self):
        """测试文档分割"""
        document = Document(
            title="测试文档",
            content=self.sample_text,
            file_type="txt",
            size=len(self.sample_text.encode('utf-8'))
        )

        result = self.engine.split_document(document)

        assert result.document_id == document.id
        assert result.total_chunks > 0
        assert len(result.chunks) == result.total_chunks
        assert result.strategy_used in ["token_based", "character_based"]
        assert result.error is None

    def test_batch_splitting(self):
        """测试批量分割"""
        documents = [
            Document(
                title=f"文档{i}",
                content=self.sample_text,
                file_type="txt",
                size=len(self.sample_text.encode('utf-8'))
            )
            for i in range(3)
        ]

        results = self.engine.batch_split(documents)

        assert len(results) == 3
        assert all(result.total_chunks > 0 for result in results)
        assert all(result.error is None for result in results)

    def test_batch_splitting_with_errors(self):
        """测试批量分割中的错误处理"""
        # 创建一个会导致错误的文档（空内容）
        documents = [
            Document(
                title="正常文档",
                content=self.sample_text,
                file_type="txt",
                size=len(self.sample_text.encode('utf-8'))
            )
        ]

        # 模拟分割过程中的错误
        with patch.object(self.engine, 'split_document') as mock_split:
            mock_split.side_effect = [
                Exception("模拟错误")
            ]

            results = self.engine.batch_split(documents)

            assert len(results) == 1
            assert results[0].error is not None
            assert results[0].total_chunks == 0
            assert results[0].strategy_used == "error"

    def test_empty_text_handling(self):
        """测试空文本处理"""
        strategy = TokenBasedStrategy(max_tokens=100)

        # 空字符串应该返回空列表
        chunks = self.engine.split_text("", strategy)
        assert len(chunks) == 0

    def test_very_short_text(self):
        """测试极短文本"""
        strategy = TokenBasedStrategy(max_tokens=100)
        short_text = "短"

        chunks = self.engine.split_text(short_text, strategy)
        assert len(chunks) == 1
        assert chunks[0].content == short_text
        assert chunks[0].start_char == 0
        assert chunks[0].end_char == 1
        assert chunks[0].token_count > 0

    def test_token_estimation_accuracy(self):
        """测试 token 估算准确性"""
        # 测试纯中文
        chinese_text = "这是一个中文测试文档"
        chunks = self.engine.split_text(chinese_text, TokenBasedStrategy())
        chinese_tokens = chunks[0].token_count

        # 测试纯英文
        english_text = "This is an English test document"
        chunks = self.engine.split_text(english_text, TokenBasedStrategy())
        english_tokens = chunks[0].token_count

        # 测试混合文本
        mixed_text = "这是中英文混合 This is mixed content"
        chunks = self.engine.split_text(mixed_text, TokenBasedStrategy())
        mixed_tokens = chunks[0].token_count

        # 验证 token 估算合理性
        assert chinese_tokens > 0
        assert english_tokens > 0
        assert mixed_tokens > 0

        # 混合文本的 token 数应该介于纯中文和纯英文之间（大致）
        assert mixed_tokens > min(chinese_tokens, english_tokens)

    def test_strategy_determination(self):
        """测试策略自动确定"""
        # 测试 Markdown 文档
        md_doc = Document(
            title="Markdown 文档",
            content="# 标题\n内容",
            file_type="markdown",
            size=10
        )
        strategy = self.engine._determine_strategy(md_doc)
        assert isinstance(strategy, CharacterBasedStrategy)

        # 测试普通文本文档
        txt_doc = Document(
            title="文本文档",
            content="普通文本内容",
            file_type="txt",
            size=15
        )
        strategy = self.engine._determine_strategy(txt_doc)
        assert isinstance(strategy, TokenBasedStrategy)

    def test_error_handling_in_split_text(self):
        """测试 split_text 中的错误处理"""
        strategy = TokenBasedStrategy()

        # 模拟分割器错误
        with patch.object(self.engine, '_get_splitter') as mock_get_splitter:
            mock_splitter = MagicMock()
            mock_splitter.chunks.side_effect = Exception("分割器错误")
            mock_get_splitter.return_value = mock_splitter

            with pytest.raises(Exception, match="分割器错误"):
                self.engine.split_text("测试文本", strategy)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

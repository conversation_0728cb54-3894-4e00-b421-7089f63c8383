"""
Backend models 单元测试
"""

import pytest
import uuid
from datetime import datetime
from sqlmodel import Session, create_engine
from sqlalchemy.pool import StaticPool

# 导入要测试的模型
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../backend'))

from app.models import (
    User, UserCreate, UserUpdate, UserPublic,
    Document, DocumentCreate, DocumentUpdate, DocumentPublic,
    DocumentChunk, DocumentChunkCreate, DocumentChunkPublic,
    Topic, TopicCreate, TopicUpdate, TopicPublic,
    Conversation, ConversationCreate, ConversationUpdate, ConversationPublic,
    Item, ItemCreate, ItemUpdate, ItemPublic
)


@pytest.fixture
def engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    return engine


@pytest.fixture
def session(engine):
    """创建测试数据库会话"""
    from app.models.base import SQLModel
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


class TestUserModel:
    """用户模型测试"""
    
    def test_user_create_model(self):
        """测试用户创建模型"""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        user_create = UserCreate(**user_data)
        
        assert user_create.email == "<EMAIL>"
        assert user_create.password == "testpassword123"
        assert user_create.full_name == "Test User"
        assert user_create.is_active is True
        assert user_create.is_superuser is False
    
    def test_user_create_validation(self):
        """测试用户创建验证"""
        # 测试无效邮箱
        with pytest.raises(ValueError):
            UserCreate(email="invalid-email", password="test123")
        
        # 测试空密码
        with pytest.raises(ValueError):
            UserCreate(email="<EMAIL>", password="")
    
    def test_user_update_model(self):
        """测试用户更新模型"""
        user_update = UserUpdate(
            email="<EMAIL>",
            full_name="Updated User"
        )
        
        assert user_update.email == "<EMAIL>"
        assert user_update.full_name == "Updated User"
        assert user_update.password is None
    
    def test_user_public_model(self):
        """测试用户公开模型"""
        user_public = UserPublic(
            id=uuid.uuid4(),
            email="<EMAIL>",
            full_name="Test User",
            is_active=True,
            is_superuser=False
        )
        
        assert user_public.email == "<EMAIL>"
        assert user_public.full_name == "Test User"
        assert user_public.is_active is True
        assert user_public.is_superuser is False


class TestDocumentModel:
    """文档模型测试"""
    
    def test_document_create_model(self):
        """测试文档创建模型"""
        doc_data = {
            "title": "Test Document",
            "content": "This is test content",
            "file_type": "txt",
            "size": 100
        }
        doc_create = DocumentCreate(**doc_data)
        
        assert doc_create.title == "Test Document"
        assert doc_create.content == "This is test content"
        assert doc_create.file_type == "txt"
        assert doc_create.size == 100
    
    def test_document_create_validation(self):
        """测试文档创建验证"""
        # 测试空标题
        with pytest.raises(ValueError):
            DocumentCreate(title="", content="test", file_type="txt", size=100)
        
        # 测试空内容
        with pytest.raises(ValueError):
            DocumentCreate(title="Test", content="", file_type="txt", size=100)
        
        # 测试负数大小
        with pytest.raises(ValueError):
            DocumentCreate(title="Test", content="test", file_type="txt", size=-1)
    
    def test_document_update_model(self):
        """测试文档更新模型"""
        doc_update = DocumentUpdate(
            title="Updated Document",
            content="Updated content"
        )
        
        assert doc_update.title == "Updated Document"
        assert doc_update.content == "Updated content"
    
    def test_document_public_model(self):
        """测试文档公开模型"""
        doc_public = DocumentPublic(
            id=uuid.uuid4(),
            title="Test Document",
            file_type="txt",
            size=100,
            owner_id=uuid.uuid4(),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert doc_public.title == "Test Document"
        assert doc_public.file_type == "txt"
        assert doc_public.size == 100


class TestDocumentChunkModel:
    """文档块模型测试"""
    
    def test_document_chunk_create_model(self):
        """测试文档块创建模型"""
        chunk_data = {
            "content": "This is a chunk",
            "chunk_index": 0,
            "start_char": 0,
            "end_char": 15,
            "token_count": 4,
            "document_id": uuid.uuid4()
        }
        chunk_create = DocumentChunkCreate(**chunk_data)
        
        assert chunk_create.content == "This is a chunk"
        assert chunk_create.chunk_index == 0
        assert chunk_create.start_char == 0
        assert chunk_create.end_char == 15
        assert chunk_create.token_count == 4
    
    def test_document_chunk_validation(self):
        """测试文档块验证"""
        doc_id = uuid.uuid4()
        
        # 测试空内容
        with pytest.raises(ValueError):
            DocumentChunkCreate(
                content="",
                chunk_index=0,
                start_char=0,
                end_char=0,
                document_id=doc_id
            )
        
        # 测试负数索引
        with pytest.raises(ValueError):
            DocumentChunkCreate(
                content="test",
                chunk_index=-1,
                start_char=0,
                end_char=4,
                document_id=doc_id
            )


class TestTopicModel:
    """主题模型测试"""
    
    def test_topic_create_model(self):
        """测试主题创建模型"""
        topic_data = {
            "name": "Test Topic",
            "description": "This is a test topic"
        }
        topic_create = TopicCreate(**topic_data)
        
        assert topic_create.name == "Test Topic"
        assert topic_create.description == "This is a test topic"
    
    def test_topic_create_validation(self):
        """测试主题创建验证"""
        # 测试空名称
        with pytest.raises(ValueError):
            TopicCreate(name="", description="test")
    
    def test_topic_update_model(self):
        """测试主题更新模型"""
        topic_update = TopicUpdate(
            name="Updated Topic",
            description="Updated description"
        )
        
        assert topic_update.name == "Updated Topic"
        assert topic_update.description == "Updated description"


class TestConversationModel:
    """对话模型测试"""
    
    def test_conversation_create_model(self):
        """测试对话创建模型"""
        conv_data = {
            "title": "Test Conversation",
            "topic_id": uuid.uuid4()
        }
        conv_create = ConversationCreate(**conv_data)
        
        assert conv_create.title == "Test Conversation"
        assert conv_create.topic_id is not None
    
    def test_conversation_create_validation(self):
        """测试对话创建验证"""
        # 测试空标题
        with pytest.raises(ValueError):
            ConversationCreate(title="", topic_id=uuid.uuid4())


class TestItemModel:
    """项目模型测试"""
    
    def test_item_create_model(self):
        """测试项目创建模型"""
        item_data = {
            "title": "Test Item",
            "description": "This is a test item"
        }
        item_create = ItemCreate(**item_data)
        
        assert item_create.title == "Test Item"
        assert item_create.description == "This is a test item"
    
    def test_item_create_validation(self):
        """测试项目创建验证"""
        # 测试空标题
        with pytest.raises(ValueError):
            ItemCreate(title="", description="test")
    
    def test_item_update_model(self):
        """测试项目更新模型"""
        item_update = ItemUpdate(
            title="Updated Item",
            description="Updated description"
        )
        
        assert item_update.title == "Updated Item"
        assert item_update.description == "Updated description"


class TestModelRelationships:
    """测试模型关系"""
    
    def test_user_document_relationship(self, session):
        """测试用户-文档关系"""
        # 创建用户
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Test User"
        )
        session.add(user)
        session.commit()
        session.refresh(user)
        
        # 创建文档
        document = Document(
            title="Test Document",
            content="Test content",
            file_type="txt",
            size=100,
            owner_id=user.id
        )
        session.add(document)
        session.commit()
        session.refresh(document)
        
        assert document.owner_id == user.id
    
    def test_document_chunk_relationship(self, session):
        """测试文档-块关系"""
        # 创建用户
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Test User"
        )
        session.add(user)
        session.commit()
        session.refresh(user)
        
        # 创建文档
        document = Document(
            title="Test Document",
            content="Test content",
            file_type="txt",
            size=100,
            owner_id=user.id
        )
        session.add(document)
        session.commit()
        session.refresh(document)
        
        # 创建文档块
        chunk = DocumentChunk(
            content="Test chunk",
            chunk_index=0,
            start_char=0,
            end_char=10,
            token_count=2,
            document_id=document.id
        )
        session.add(chunk)
        session.commit()
        session.refresh(chunk)
        
        assert chunk.document_id == document.id

"""
文档服务集成测试
测试文档服务与数据库的集成
"""

import pytest
import sys
import uuid
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 模拟数据库相关的导入
class MockSession:
    """模拟数据库会话"""
    def __init__(self):
        self.documents = {}
        self.chunks = {}
        self.committed = False
        self.rolled_back = False
    
    def add(self, obj):
        if hasattr(obj, 'id'):
            if hasattr(obj, 'document_id'):  # 这是一个 chunk
                self.chunks[obj.id] = obj
            else:  # 这是一个 document
                self.documents[obj.id] = obj
    
    def commit(self):
        self.committed = True
    
    def rollback(self):
        self.rolled_back = True
    
    def query(self, model):
        return MockQuery(self, model)
    
    def get(self, model, id):
        if model.__name__ == 'Document':
            return self.documents.get(id)
        elif model.__name__ == 'DocumentChunk':
            return self.chunks.get(id)
        return None

class MockQuery:
    """模拟查询对象"""
    def __init__(self, session, model):
        self.session = session
        self.model = model
        self._filters = []
    
    def filter(self, *args):
        self._filters.extend(args)
        return self
    
    def all(self):
        if self.model.__name__ == 'Document':
            return list(self.session.documents.values())
        elif self.model.__name__ == 'DocumentChunk':
            return list(self.session.chunks.values())
        return []
    
    def first(self):
        results = self.all()
        return results[0] if results else None
    
    def delete(self):
        # 模拟删除操作
        if self.model.__name__ == 'DocumentChunk':
            self.session.chunks.clear()

# 模拟模型类
class MockDocument:
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', uuid.uuid4())
        self.title = kwargs.get('title', '')
        self.content = kwargs.get('content', '')
        self.file_type = kwargs.get('file_type', 'txt')
        self.size = kwargs.get('size', 0)
        self.created_at = kwargs.get('created_at', datetime.now())

class MockDocumentChunk:
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', uuid.uuid4())
        self.document_id = kwargs.get('document_id')
        self.content = kwargs.get('content', '')
        self.chunk_index = kwargs.get('chunk_index', 0)
        self.start_char = kwargs.get('start_char', 0)
        self.end_char = kwargs.get('end_char', 0)
        self.token_count = kwargs.get('token_count', 0)

class MockDocumentChunkCreate:
    def __init__(self, **kwargs):
        self.document_id = kwargs.get('document_id')
        self.content = kwargs.get('content', '')
        self.chunk_index = kwargs.get('chunk_index', 0)
        self.start_char = kwargs.get('start_char', 0)
        self.end_char = kwargs.get('end_char', 0)
        self.token_count = kwargs.get('token_count', 0)


class TestDocumentServiceIntegration:
    """文档服务集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.mock_session = MockSession()
        
        # 创建测试文档
        self.test_document = MockDocument(
            title="集成测试文档",
            content="这是一个用于集成测试的文档内容。" * 20,
            file_type="txt",
            size=240
        )
        
        # 模拟数据库操作函数
        self.mock_create_document_chunk = Mock(side_effect=self._create_chunk)
        self.mock_delete_document_chunks = Mock()
    
    def _create_chunk(self, session, chunk_in):
        """模拟创建文档块"""
        chunk = MockDocumentChunk(
            document_id=chunk_in.document_id,
            content=chunk_in.content,
            chunk_index=chunk_in.chunk_index,
            start_char=chunk_in.start_char,
            end_char=chunk_in.end_char,
            token_count=chunk_in.token_count
        )
        session.add(chunk)
        return chunk
    
    @patch('engines.text_splitter.engine.TextSplitterEngine')
    def test_chunk_service_integration(self, mock_engine_class):
        """测试分块服务集成"""
        # 设置模拟引擎
        mock_engine = Mock()
        mock_engine_class.return_value = mock_engine
        
        # 模拟分割结果
        from engines.text_splitter.models import TextChunk, SplitResult
        
        mock_chunks = [
            TextChunk(
                content="这是第一个分块内容。",
                chunk_index=0,
                start_char=0,
                end_char=10,
                token_count=5
            ),
            TextChunk(
                content="这是第二个分块内容。",
                chunk_index=1,
                start_char=10,
                end_char=20,
                token_count=5
            )
        ]
        
        mock_result = SplitResult(
            document_id=str(self.test_document.id),
            chunks=mock_chunks,
            strategy_used="token_based",
            total_chunks=2
        )
        
        mock_engine.split_document.return_value = mock_result
        
        # 模拟 ChunkService
        with patch('sys.path'), \
             patch.dict('sys.modules', {
                 'app.models': Mock(),
                 'app.crud.document': Mock(),
             }):
            
            # 创建模拟的 ChunkService
            chunk_service = Mock()
            chunk_service.session = self.mock_session
            chunk_service.text_splitter = mock_engine
            
            # 模拟 split_document 方法
            def mock_split_document(document, strategy=None):
                # 执行分割
                engine_doc = Mock()
                engine_doc.id = str(document.id)
                engine_doc.title = document.title
                engine_doc.content = document.content
                engine_doc.file_type = document.file_type
                engine_doc.size = document.size
                
                split_result = mock_engine.split_document(engine_doc, strategy)
                
                # 清除现有分块
                self.mock_delete_document_chunks(session=self.mock_session, document_id=document.id)
                
                # 保存新分块
                db_chunks = []
                for chunk in split_result.chunks:
                    chunk_create = MockDocumentChunkCreate(
                        document_id=document.id,
                        content=chunk.content,
                        chunk_index=chunk.chunk_index,
                        start_char=chunk.start_char,
                        end_char=chunk.end_char,
                        token_count=chunk.token_count
                    )
                    
                    db_chunk = self._create_chunk(self.mock_session, chunk_create)
                    db_chunks.append(db_chunk)
                
                return db_chunks
            
            chunk_service.split_document = mock_split_document
            
            # 执行分割
            result_chunks = chunk_service.split_document(self.test_document)
            
            # 验证结果
            assert len(result_chunks) == 2
            assert all(chunk.document_id == self.test_document.id for chunk in result_chunks)
            assert result_chunks[0].chunk_index == 0
            assert result_chunks[1].chunk_index == 1
            
            # 验证数据库操作
            self.mock_delete_document_chunks.assert_called_once()
            assert len(self.mock_session.chunks) == 2
    
    def test_processing_service_integration(self):
        """测试处理服务集成"""
        # 模拟处理服务
        processing_service = Mock()
        
        # 模拟处理结果
        def mock_process_document(document_id):
            return {
                "success": True,
                "document_id": document_id,
                "document_title": self.test_document.title,
                "chunks_created": 3,
                "strategy_used": "TokenBasedStrategy",
                "statistics": {
                    "total_chunks": 3,
                    "avg_chunk_size": 80,
                    "total_tokens": 45
                },
                "message": f"Successfully processed document '{self.test_document.title}' into 3 chunks"
            }
        
        processing_service.process_document = mock_process_document
        
        # 执行处理
        result = processing_service.process_document(str(self.test_document.id))
        
        # 验证结果
        assert result["success"] == True
        assert result["document_id"] == str(self.test_document.id)
        assert result["chunks_created"] == 3
        assert "statistics" in result
        assert result["statistics"]["total_chunks"] == 3
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 模拟处理服务
        processing_service = Mock()
        
        # 模拟处理错误
        def mock_process_document_with_error(document_id):
            return {
                "success": False,
                "document_id": document_id,
                "error": "Document not found",
                "message": f"Failed to process document {document_id}: Document not found"
            }
        
        processing_service.process_document = mock_process_document_with_error
        
        # 执行处理
        result = processing_service.process_document("non-existent-id")
        
        # 验证错误处理
        assert result["success"] == False
        assert "error" in result
        assert result["error"] == "Document not found"
    
    def test_transaction_handling(self):
        """测试事务处理"""
        # 模拟事务成功的情况
        with patch('sys.path'):
            # 正常操作
            self.mock_session.add(self.test_document)
            self.mock_session.commit()
            
            assert self.mock_session.committed == True
            assert self.mock_session.rolled_back == False
        
        # 模拟事务失败的情况
        mock_session_with_error = MockSession()
        
        def mock_commit_with_error():
            raise Exception("Database error")
        
        mock_session_with_error.commit = mock_commit_with_error
        
        try:
            mock_session_with_error.add(self.test_document)
            mock_session_with_error.commit()
        except Exception:
            mock_session_with_error.rollback()
        
        assert mock_session_with_error.rolled_back == True
    
    def test_concurrent_processing_simulation(self):
        """测试并发处理模拟"""
        import threading
        import time
        
        results = []
        errors = []
        
        def process_document(doc_id, delay=0):
            try:
                time.sleep(delay)  # 模拟处理时间
                
                # 模拟处理逻辑
                result = {
                    "document_id": doc_id,
                    "success": True,
                    "chunks_created": 2,
                    "processing_time": delay
                }
                results.append(result)
            except Exception as e:
                errors.append({"document_id": doc_id, "error": str(e)})
        
        # 创建多个线程模拟并发处理
        threads = []
        for i in range(5):
            doc_id = f"doc_{i}"
            thread = threading.Thread(target=process_document, args=(doc_id, 0.1))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 5
        assert len(errors) == 0
        assert all(r["success"] for r in results)
    
    def test_data_consistency_validation(self):
        """测试数据一致性验证"""
        # 创建文档和分块
        document = MockDocument(
            title="一致性测试文档",
            content="测试内容" * 10,
            file_type="txt",
            size=40
        )
        
        chunks = [
            MockDocumentChunk(
                document_id=document.id,
                content="测试内容测试内容",
                chunk_index=0,
                start_char=0,
                end_char=8,
                token_count=4
            ),
            MockDocumentChunk(
                document_id=document.id,
                content="测试内容测试内容",
                chunk_index=1,
                start_char=8,
                end_char=16,
                token_count=4
            )
        ]
        
        # 验证数据一致性
        total_chunk_content = "".join(chunk.content for chunk in chunks)
        
        # 验证分块内容的总长度
        assert len(total_chunk_content) <= len(document.content)
        
        # 验证分块索引的连续性
        chunk_indices = [chunk.chunk_index for chunk in chunks]
        assert chunk_indices == list(range(len(chunks)))
        
        # 验证字符位置的连续性
        for i in range(len(chunks) - 1):
            assert chunks[i].end_char <= chunks[i + 1].start_char
        
        # 验证 token 计数的合理性
        total_tokens = sum(chunk.token_count for chunk in chunks)
        assert total_tokens > 0
        assert total_tokens < len(document.content)  # token 数应该少于字符数


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

"""
文档分割功能集成测试
测试完整的文档分割流程，包括数据库交互
"""

import pytest
import sys
import uuid
import tempfile
from pathlib import Path
from typing import Generator
from unittest.mock import patch

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from engines.text_splitter.engine import TextSplitterEngine
from engines.text_splitter.models import Document as EngineDocument
from engines.text_splitter.strategies import TokenBasedStrategy, CharacterBasedStrategy


class TestDocumentSplittingIntegration:
    """文档分割集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.engine = TextSplitterEngine()
        
        # 创建测试文档内容
        self.test_documents = {
            "short_text": {
                "title": "短文档",
                "content": "这是一个短文档，用于测试基本的分割功能。",
                "file_type": "txt"
            },
            "long_text": {
                "title": "长文档",
                "content": "这是一个很长的文档。" * 200,  # 约 2000 字符
                "file_type": "txt"
            },
            "markdown": {
                "title": "Markdown 文档",
                "content": """# 主标题

这是一个 Markdown 文档的示例。

## 子标题 1

这里是第一个子章节的内容。包含一些文本来测试分割功能。

### 子子标题

更详细的内容在这里。

## 子标题 2

第二个子章节的内容。

- 列表项 1
- 列表项 2
- 列表项 3

```python
def example_function():
    return "Hello, World!"
```

这是代码块后的文本。
""",
                "file_type": "md"
            },
            "mixed_language": {
                "title": "中英文混合文档",
                "content": """这是一个中英文混合的文档。This is a mixed language document.

中文部分：这里包含了一些中文内容，用来测试中文文本的分割效果。
English part: This section contains English content to test the splitting effectiveness for English text.

混合段落：This paragraph contains both 中文 and English content mixed together. 这样可以测试混合语言的处理能力。

Technical terms: API, HTTP, JSON, XML, SQL, NoSQL
技术术语：应用程序接口、超文本传输协议、数据格式

Numbers and symbols: 123, 456, @#$%^&*()
数字和符号：一二三、四五六、！@#￥%……&*（）
""",
                "file_type": "txt"
            }
        }
    
    def create_engine_document(self, doc_data: dict) -> EngineDocument:
        """创建引擎文档对象"""
        content = doc_data["content"]
        return EngineDocument(
            title=doc_data["title"],
            content=content,
            file_type=doc_data["file_type"],
            size=len(content.encode('utf-8'))
        )
    
    def test_end_to_end_document_splitting(self):
        """测试端到端的文档分割流程"""
        for doc_name, doc_data in self.test_documents.items():
            # 创建文档
            doc = self.create_engine_document(doc_data)

            # 执行分割
            result = self.engine.split_document(doc)

            # 验证结果
            assert result is not None
            assert result.document_id == doc.id
            assert result.total_chunks > 0
            assert len(result.chunks) == result.total_chunks
            assert result.error is None

            # 验证分块质量
            for i, chunk in enumerate(result.chunks):
                assert chunk.chunk_index == i
                assert chunk.content.strip()  # 内容不为空
                assert chunk.start_char >= 0
                assert chunk.end_char > chunk.start_char
                assert chunk.token_count is not None
                assert chunk.token_count > 0

            print(f"✓ {doc_name}: {result.total_chunks} chunks created")
    
    def test_different_strategies_integration(self):
        """测试不同策略的集成"""
        doc = self.create_engine_document(self.test_documents["long_text"])
        
        strategies = [
            TokenBasedStrategy(max_tokens=100),
            TokenBasedStrategy(max_tokens=500),
            CharacterBasedStrategy(max_chars=200),
            CharacterBasedStrategy(max_chars=1000)
        ]
        
        results = []
        for strategy in strategies:
            result = self.engine.split_document(doc, strategy)
            results.append(result)
            
            assert result.total_chunks > 0
            assert result.strategy_used == strategy.name
            
        # 验证不同策略产生不同的分块数量
        chunk_counts = [r.total_chunks for r in results]
        assert len(set(chunk_counts)) > 1  # 至少有两种不同的分块数量
    
    def test_batch_processing_integration(self):
        """测试批量处理集成"""
        documents = [
            self.create_engine_document(doc_data)
            for doc_data in self.test_documents.values()
        ]
        
        results = self.engine.batch_split(documents)
        
        assert len(results) == len(documents)
        
        # 验证所有文档都被成功处理
        successful_results = [r for r in results if r.error is None]
        assert len(successful_results) == len(documents)
        
        # 验证每个结果的完整性
        for result in successful_results:
            assert result.total_chunks > 0
            assert len(result.chunks) == result.total_chunks
    
    def test_error_recovery_integration(self):
        """测试错误恢复集成"""
        # 创建一个会导致错误的文档
        problematic_doc = EngineDocument(
            title="问题文档",
            content="这会导致错误的内容",
            file_type="txt",
            size=24
        )

        normal_doc = self.create_engine_document(self.test_documents["short_text"])

        # 直接使用 batch_split 的错误处理机制
        # 通过修改引擎的 split_document 方法来模拟错误
        original_split_document = self.engine.split_document

        def mock_split_document(doc, strategy=None):
            if "这会导致错误的内容" in doc.content:
                raise Exception("模拟的分割错误")
            return original_split_document(doc, strategy)

        self.engine.split_document = mock_split_document

        try:
            # 批量处理包含问题文档的列表
            results = self.engine.batch_split([problematic_doc, normal_doc])

            assert len(results) == 2

            # 第一个文档应该有错误
            assert results[0].error is not None
            assert results[0].total_chunks == 0
            assert results[0].strategy_used == "error"

            # 第二个文档应该成功处理
            assert results[1].error is None
            assert results[1].total_chunks > 0

        finally:
            # 恢复原始方法
            self.engine.split_document = original_split_document
    
    def test_large_document_processing(self):
        """测试大文档处理"""
        # 创建一个很大的文档
        large_content = "这是一个大文档的段落。" * 1000  # 约 12000 字符
        large_doc = EngineDocument(
            title="大文档",
            content=large_content,
            file_type="txt",
            size=len(large_content.encode('utf-8'))
        )
        
        # 使用小的分块大小来确保产生多个分块
        strategy = CharacterBasedStrategy(max_chars=500)
        result = self.engine.split_document(large_doc, strategy)
        
        assert result.total_chunks > 10  # 应该产生很多分块
        
        # 验证分块的连续性
        total_content_length = 0
        for chunk in result.chunks:
            total_content_length += len(chunk.content)
        
        # 总长度应该接近原文档长度（允许一些误差）
        original_length = len(large_content)
        assert abs(total_content_length - original_length) < original_length * 0.1
    
    def test_special_characters_integration(self):
        """测试特殊字符集成处理"""
        special_doc_data = {
            "title": "特殊字符文档",
            "content": """特殊字符测试文档

Unicode 字符：😀🎉🚀💻🌟
数学符号：∑∏∫∆∇∂
希腊字母：αβγδεζηθικλμνξοπρστυφχψω
特殊标点：""''—…‰‱
货币符号：￥€$£¢

编程符号：
- 操作符：+ - * / % ^ & | ~ << >>
- 括号：() [] {} <> 
- 引号：' " ` 
- 其他：@ # $ % ^ & * _ + = | \ : ; " ' < > , . ? /

HTML 实体：&lt; &gt; &amp; &quot; &#39;
XML 转义：&apos; &nbsp;

正则表达式：^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$
""",
            "file_type": "txt"
        }
        
        doc = self.create_engine_document(special_doc_data)
        result = self.engine.split_document(doc)
        
        assert result.total_chunks > 0
        assert result.error is None
        
        # 验证特殊字符被正确处理
        all_content = "".join(chunk.content for chunk in result.chunks)
        assert "😀🎉🚀💻🌟" in all_content
        assert "αβγδεζηθικλμνξοπρστυφχψω" in all_content
    
    def test_empty_and_whitespace_handling(self):
        """测试空内容和空白字符处理"""
        # 这些文档应该在创建时就失败，因为有验证器
        with pytest.raises(ValueError):
            EngineDocument(
                title="空文档",
                content="",
                file_type="txt",
                size=0
            )
        
        with pytest.raises(ValueError):
            EngineDocument(
                title="空白文档",
                content="   \n\t  ",
                file_type="txt",
                size=7
            )
    
    def test_performance_characteristics(self):
        """测试性能特征"""
        import time
        
        # 创建不同大小的文档
        sizes = [100, 500, 1000, 2000]  # 字符数
        processing_times = []
        
        for size in sizes:
            content = "测试内容。" * (size // 5)  # 每个重复约5字符
            doc = EngineDocument(
                title=f"性能测试文档_{size}",
                content=content,
                file_type="txt",
                size=len(content.encode('utf-8'))
            )
            
            start_time = time.time()
            result = self.engine.split_document(doc)
            end_time = time.time()
            
            processing_time = end_time - start_time
            processing_times.append(processing_time)
            
            assert result.total_chunks > 0
            assert processing_time < 5.0  # 处理时间应该在5秒内
            
            print(f"Size {size}: {processing_time:.3f}s, {result.total_chunks} chunks")
        
        # 验证处理时间随文档大小合理增长
        # 最大的文档处理时间不应该超过最小的10倍
        assert max(processing_times) < min(processing_times) * 10


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

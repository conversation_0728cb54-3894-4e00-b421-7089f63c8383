#!/usr/bin/env python3
"""
测试数据验证修复
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_text_chunk_validation():
    """测试 TextChunk 验证器修复"""
    print("Testing TextChunk validation fixes...")
    
    try:
        from engines.text_splitter.models import TextChunk
        
        # 测试正常情况
        chunk = TextChunk(
            content="这是一个测试文本块",
            chunk_index=0,
            start_char=0,
            end_char=9,
            token_count=5
        )
        print("✓ Normal TextChunk creation successful")
        
        # 测试空内容验证
        try:
            TextChunk(
                content="",
                chunk_index=0,
                start_char=0,
                end_char=0,
                token_count=0
            )
            print("✗ Empty content validation failed")
            return False
        except ValueError as e:
            print(f"✓ Empty content validation works: {e}")
        
        # 测试负数位置验证
        try:
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=-1,
                end_char=4,
                token_count=1
            )
            print("✗ Negative position validation failed")
            return False
        except ValueError as e:
            print(f"✓ Negative position validation works: {e}")
        
        # 测试负数 token_count 验证
        try:
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=0,
                end_char=4,
                token_count=-1
            )
            print("✗ Negative token count validation failed")
            return False
        except ValueError as e:
            print(f"✓ Negative token count validation works: {e}")
        
        # 测试 end_char <= start_char 验证
        try:
            TextChunk(
                content="test",
                chunk_index=0,
                start_char=5,
                end_char=4,
                token_count=1
            )
            print("✗ end_char <= start_char validation failed")
            return False
        except ValueError as e:
            print(f"✓ end_char <= start_char validation works: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ TextChunk validation test failed: {e}")
        return False


def test_document_validation():
    """测试 Document 验证器修复"""
    print("\nTesting Document validation fixes...")
    
    try:
        from engines.text_splitter.models import Document
        
        # 测试正常情况
        content = "这是一个测试文档内容"
        doc = Document(
            title="测试文档",
            content=content,
            file_type="txt",
            size=len(content.encode('utf-8'))
        )
        print("✓ Normal Document creation successful")
        
        # 测试空标题验证
        try:
            Document(
                title="",
                content="test content",
                file_type="txt",
                size=12
            )
            print("✗ Empty title validation failed")
            return False
        except ValueError as e:
            print(f"✓ Empty title validation works: {e}")
        
        # 测试空内容验证
        try:
            Document(
                title="Test",
                content="",
                file_type="txt",
                size=0
            )
            print("✗ Empty content validation failed")
            return False
        except ValueError as e:
            print(f"✓ Empty content validation works: {e}")
        
        # 测试负数大小验证
        try:
            Document(
                title="Test",
                content="test content",
                file_type="txt",
                size=-1
            )
            print("✗ Negative size validation failed")
            return False
        except ValueError as e:
            print(f"✓ Negative size validation works: {e}")
        
        # 测试大小不匹配（应该只警告，不抛异常）
        doc_with_size_mismatch = Document(
            title="Test",
            content="test content",
            file_type="txt",
            size=1000  # 明显不匹配的大小
        )
        print("✓ Size mismatch handled gracefully (warning only)")
        
        return True
        
    except Exception as e:
        print(f"✗ Document validation test failed: {e}")
        return False


def test_engine_integration():
    """测试引擎集成"""
    print("\nTesting engine integration...")
    
    try:
        from engines.text_splitter.engine import TextSplitterEngine
        from engines.text_splitter.models import Document
        from engines.text_splitter.strategies import TokenBasedStrategy
        
        # 创建引擎
        engine = TextSplitterEngine()
        print("✓ Engine created successfully")
        
        # 创建测试文档
        content = "这是一个测试文档。" * 20  # 创建较长的内容
        doc = Document(
            title="测试文档",
            content=content,
            file_type="txt",
            size=len(content.encode('utf-8'))
        )
        
        # 测试分割
        strategy = TokenBasedStrategy(max_tokens=50)
        result = engine.split_document(doc, strategy)
        
        print(f"✓ Document split into {result.total_chunks} chunks")
        
        # 验证分块的数据完整性
        for i, chunk in enumerate(result.chunks):
            if chunk.chunk_index != i:
                print(f"✗ Chunk index mismatch at {i}")
                return False
            
            if chunk.token_count is None or chunk.token_count < 0:
                print(f"✗ Invalid token count in chunk {i}")
                return False
            
            if chunk.end_char <= chunk.start_char:
                print(f"✗ Invalid character positions in chunk {i}")
                return False
        
        print("✓ All chunks have valid data")
        return True
        
    except Exception as e:
        print(f"✗ Engine integration test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("数据验证修复测试")
    print("=" * 60)
    
    tests = [
        test_text_chunk_validation,
        test_document_validation,
        test_engine_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有验证修复测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

"""
对话服务 POC 主入口
"""
import uuid
from datetime import datetime
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware

from simple_models import (
    ConversationCreate, ConversationUpdate, ConversationPublic,
    ConversationMessagePublic, ChatRequest, ChatResponse,
    LearningProgress
)
from services import ConversationOrchestrator, ConversationService


# 全局服务实例
orchestrator = None
conversation_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global orchestrator, conversation_service
    
    print("🚀 启动 Conversation Service POC...")
    
    # 初始化服务
    orchestrator = ConversationOrchestrator()
    conversation_service = orchestrator.conversation_service
    
    print("✅ Conversation Service POC 初始化完成")
    
    yield
    
    print("🛑 关闭 Conversation Service POC...")


# 创建 FastAPI 应用
app = FastAPI(
    title="Conversation Service POC",
    description="对话管理服务概念验证",
    version="1.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 依赖注入
def get_orchestrator() -> ConversationOrchestrator:
    if orchestrator is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return orchestrator


def get_conversation_service() -> ConversationService:
    if conversation_service is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return conversation_service


# API 路由
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "conversation-poc",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/conversations", response_model=ConversationPublic)
async def create_conversation(
    conversation_in: ConversationCreate,
    user_id: str = "default-user",  # 简化用户认证
    service: ConversationService = Depends(get_conversation_service)
):
    """创建新对话"""
    try:
        user_uuid = uuid.UUID(user_id) if user_id != "default-user" else uuid.uuid4()
        conversation = service.create_conversation(user_uuid, conversation_in)
        
        return ConversationPublic(
            id=conversation.id,
            user_id=conversation.user_id,
            title=conversation.title,
            topic_id=conversation.topic_id,
            status=conversation.status,
            learning_level=conversation.learning_level,
            context=conversation.context,
            metadata=conversation.metadata,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/conversations/{conversation_id}", response_model=ConversationPublic)
async def get_conversation(
    conversation_id: uuid.UUID,
    service: ConversationService = Depends(get_conversation_service)
):
    """获取对话详情"""
    conversation = service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@app.put("/conversations/{conversation_id}", response_model=ConversationPublic)
async def update_conversation(
    conversation_id: uuid.UUID,
    update_data: ConversationUpdate,
    service: ConversationService = Depends(get_conversation_service)
):
    """更新对话"""
    conversation = service.update_conversation(conversation_id, update_data)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@app.get("/conversations/{conversation_id}/messages", response_model=List[ConversationMessagePublic])
async def get_conversation_messages(
    conversation_id: uuid.UUID,
    limit: int = 50,
    service: ConversationService = Depends(get_conversation_service)
):
    """获取对话消息"""
    messages = service.get_messages(conversation_id, limit)
    
    return [
        ConversationMessagePublic(
            id=msg.id,
            conversation_id=msg.conversation_id,
            role=msg.role,
            content=msg.content,
            context_used=msg.context_used,
            metadata=msg.metadata,
            created_at=msg.created_at
        )
        for msg in messages
    ]


@app.post("/conversations/{conversation_id}/chat", response_model=ChatResponse)
async def chat(
    conversation_id: uuid.UUID,
    chat_request: ChatRequest,
    orchestrator: ConversationOrchestrator = Depends(get_orchestrator)
):
    """发送聊天消息"""
    try:
        response = await orchestrator.handle_chat(conversation_id, chat_request)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/conversations/{conversation_id}/progress", response_model=LearningProgress)
async def get_learning_progress(
    conversation_id: uuid.UUID,
    service: ConversationService = Depends(get_conversation_service)
):
    """获取学习进度"""
    # 模拟学习进度
    return LearningProgress(
        knowledge_points_covered=["基础概念", "实际应用", "高级技巧"],
        mastery_levels={"基础概念": 0.8, "实际应用": 0.6, "高级技巧": 0.3},
        questions_asked=15,
        correct_answers=12,
        session_duration=1800,
        engagement_score=0.85
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)

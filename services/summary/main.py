"""
摘要服务 POC 主入口
"""
import uuid
from datetime import datetime
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware

from simple_models import (
    SummaryCreate, SummaryUpdate, SummaryPublic,
    SummarizeRequest, SummarizeResponse,
    ConversationSummaryRequest, ConversationSummaryResponse,
    LearningReport, TextAnalysis
)
from services import SummaryService, TextSummarizationService, ConversationSummaryService
from processors import TextProcessor


# 全局服务实例
summary_service = None
text_summarization_service = None
conversation_summary_service = None
text_processor = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global summary_service, text_summarization_service, conversation_summary_service, text_processor
    
    print("🚀 启动 Summary Service POC...")
    
    # 初始化服务
    summary_service = SummaryService()
    text_summarization_service = TextSummarizationService()
    conversation_summary_service = ConversationSummaryService()
    text_processor = TextProcessor()
    
    print("✅ Summary Service POC 初始化完成")
    
    yield
    
    print("🛑 关闭 Summary Service POC...")


# 创建 FastAPI 应用
app = FastAPI(
    title="Summary Service POC",
    description="摘要生成服务概念验证",
    version="1.0.0",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 依赖注入
def get_summary_service() -> SummaryService:
    if summary_service is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return summary_service


def get_text_summarization_service() -> TextSummarizationService:
    if text_summarization_service is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return text_summarization_service


def get_conversation_summary_service() -> ConversationSummaryService:
    if conversation_summary_service is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return conversation_summary_service


def get_text_processor() -> TextProcessor:
    if text_processor is None:
        raise HTTPException(status_code=500, detail="Service not initialized")
    return text_processor


# API 路由
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "summary-poc",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/analyze", response_model=TextAnalysis)
async def analyze_text(
    content: str,
    processor: TextProcessor = Depends(get_text_processor)
):
    """分析文本"""
    try:
        analysis = processor.analyze_text(content)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/summarize", response_model=SummarizeResponse)
async def generate_summary(
    request: SummarizeRequest,
    service: TextSummarizationService = Depends(get_text_summarization_service)
):
    """生成文本摘要"""
    try:
        response = await service.generate_summary(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/summaries", response_model=SummaryPublic)
async def create_summary(
    summary_in: SummaryCreate,
    user_id: str = "default-user",  # 简化用户认证
    service: SummaryService = Depends(get_summary_service)
):
    """创建摘要记录"""
    try:
        user_uuid = uuid.UUID(user_id) if user_id != "default-user" else uuid.uuid4()
        summary = service.create_summary(user_uuid, summary_in)
        
        return SummaryPublic(
            id=summary.id,
            user_id=summary.user_id,
            title=summary.title,
            content=summary.content,
            summary_type=summary.summary_type,
            strategy=summary.strategy,
            source_id=summary.source_id,
            metadata=summary.metadata,
            created_at=summary.created_at,
            updated_at=summary.updated_at
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/summaries/{summary_id}", response_model=SummaryPublic)
async def get_summary(
    summary_id: uuid.UUID,
    service: SummaryService = Depends(get_summary_service)
):
    """获取摘要详情"""
    summary = service.get_summary(summary_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    return SummaryPublic(
        id=summary.id,
        user_id=summary.user_id,
        title=summary.title,
        content=summary.content,
        summary_type=summary.summary_type,
        strategy=summary.strategy,
        source_id=summary.source_id,
        metadata=summary.metadata,
        created_at=summary.created_at,
        updated_at=summary.updated_at
    )


@app.post("/conversations/{conversation_id}/summary", response_model=ConversationSummaryResponse)
async def summarize_conversation(
    conversation_id: uuid.UUID,
    request: ConversationSummaryRequest,
    service: ConversationSummaryService = Depends(get_conversation_summary_service)
):
    """生成对话摘要"""
    try:
        # 设置对话ID
        request.conversation_id = conversation_id
        response = await service.summarize_conversation(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/reports/learning", response_model=LearningReport)
async def get_learning_report(
    user_id: str = "default-user",
    period_days: int = 7
):
    """获取学习报告"""
    try:
        user_uuid = uuid.UUID(user_id) if user_id != "default-user" else uuid.uuid4()
        
        # 模拟学习报告
        report = LearningReport(
            user_id=user_uuid,
            period_start=datetime.utcnow(),
            period_end=datetime.utcnow(),
            total_sessions=15,
            total_duration=3600,  # 1小时
            documents_studied=8,
            conversations_completed=12,
            knowledge_points_learned=25,
            mastery_improvement={
                "Python基础": 0.3,
                "数据结构": 0.2,
                "算法": 0.1
            },
            strengths=["理论理解能力强", "学习积极性高"],
            areas_for_improvement=["实践应用需加强", "复杂概念理解需深入"],
            recommendations=[
                "增加编程实践练习",
                "尝试完成小项目",
                "复习已学知识点"
            ],
            progress_score=0.75
        )
        
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)

"""
文本处理器模块
"""
import re
import math
from typing import List, Dict, Any, Tuple
from collections import Counter

try:
    import nltk
    from nltk.tokenize import sent_tokenize, word_tokenize
    from nltk.corpus import stopwords
    from nltk.stem import SnowballStemmer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    import textstat
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False

from simple_models import TextAnalysis, ContentInsights, KnowledgePointType


class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        self.chinese_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '可以', '什么', '如果', '但是', '因为', '所以'
        }
        
        if NLTK_AVAILABLE:
            try:
                # 下载必要的 NLTK 数据
                nltk.download('punkt', quiet=True)
                nltk.download('stopwords', quiet=True)
                self.stemmer = SnowballStemmer('english')
                self.english_stopwords = set(stopwords.words('english'))
            except:
                self.stemmer = None
                self.english_stopwords = set()
    
    def analyze_text(self, text: str) -> TextAnalysis:
        """分析文本基础指标"""
        # 基础统计
        word_count = len(text.split())
        sentence_count = len(self._split_sentences(text))
        paragraph_count = len([p for p in text.split('\n\n') if p.strip()])
        
        # 预估阅读时间（中文约250字/分钟，英文约200词/分钟）
        if self._is_chinese_text(text):
            reading_time = math.ceil(len(text) / 250 * 60)  # 秒
        else:
            reading_time = math.ceil(word_count / 200 * 60)  # 秒
        
        # 可读性评分
        readability_score = self._calculate_readability(text)
        complexity_level = self._get_complexity_level(readability_score)
        
        # 提取关键短语
        key_phrases = self._extract_key_phrases(text)
        
        # 语言检测
        language = "zh" if self._is_chinese_text(text) else "en"
        
        return TextAnalysis(
            word_count=word_count,
            sentence_count=sentence_count,
            paragraph_count=paragraph_count,
            reading_time=reading_time,
            readability_score=readability_score,
            complexity_level=complexity_level,
            language=language,
            key_phrases=key_phrases
        )
    
    def extract_content_insights(self, text: str) -> ContentInsights:
        """提取内容洞察"""
        # 主要主题
        main_themes = self._extract_themes(text)
        
        # 概念层次结构
        concept_hierarchy = self._build_concept_hierarchy(text)
        
        # 难度分布
        difficulty_distribution = self._analyze_difficulty_distribution(text)
        
        # 学习目标
        learning_objectives = self._extract_learning_objectives(text)
        
        # 前置知识
        prerequisite_knowledge = self._identify_prerequisites(text)
        
        # 后续主题
        follow_up_topics = self._suggest_follow_up_topics(text)
        
        return ContentInsights(
            main_themes=main_themes,
            concept_hierarchy=concept_hierarchy,
            difficulty_distribution=difficulty_distribution,
            learning_objectives=learning_objectives,
            prerequisite_knowledge=prerequisite_knowledge,
            follow_up_topics=follow_up_topics
        )
    
    def _split_sentences(self, text: str) -> List[str]:
        """分割句子"""
        if NLTK_AVAILABLE:
            try:
                return sent_tokenize(text)
            except:
                pass
        
        # 简单的句子分割
        sentences = re.split(r'[.!?。！？]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _is_chinese_text(self, text: str) -> bool:
        """检测是否为中文文本"""
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        return chinese_chars > len(text) * 0.3
    
    def _calculate_readability(self, text: str) -> float:
        """计算可读性评分"""
        if TEXTSTAT_AVAILABLE and not self._is_chinese_text(text):
            try:
                # 使用 Flesch Reading Ease 评分
                score = textstat.flesch_reading_ease(text)
                return max(0.0, min(1.0, score / 100.0))
            except:
                pass
        
        # 简单的可读性评估
        sentences = self._split_sentences(text)
        if not sentences:
            return 0.5
        
        avg_sentence_length = len(text.split()) / len(sentences)
        
        # 基于平均句长的简单评分
        if avg_sentence_length < 10:
            return 0.9  # 很容易
        elif avg_sentence_length < 15:
            return 0.7  # 容易
        elif avg_sentence_length < 20:
            return 0.5  # 中等
        elif avg_sentence_length < 25:
            return 0.3  # 困难
        else:
            return 0.1  # 很困难
    
    def _get_complexity_level(self, readability_score: float) -> str:
        """获取复杂度等级"""
        if readability_score >= 0.8:
            return "简单"
        elif readability_score >= 0.6:
            return "容易"
        elif readability_score >= 0.4:
            return "中等"
        elif readability_score >= 0.2:
            return "困难"
        else:
            return "很困难"
    
    def _extract_key_phrases(self, text: str, max_phrases: int = 10) -> List[str]:
        """提取关键短语"""
        # 简单的关键词提取
        words = re.findall(r'\b\w+\b', text.lower())
        
        # 过滤停用词
        if self._is_chinese_text(text):
            filtered_words = [w for w in words if w not in self.chinese_stopwords and len(w) > 1]
        else:
            filtered_words = [w for w in words if w not in self.english_stopwords and len(w) > 2]
        
        # 统计词频
        word_freq = Counter(filtered_words)
        
        # 返回最频繁的词作为关键短语
        return [word for word, _ in word_freq.most_common(max_phrases)]
    
    def _extract_themes(self, text: str) -> List[str]:
        """提取主要主题"""
        # 基于关键词聚类的简单主题提取
        key_phrases = self._extract_key_phrases(text, 20)
        
        # 简单的主题分组（实际应用中可以使用更复杂的算法）
        themes = []
        
        # 技术相关主题
        tech_keywords = ['python', 'programming', '编程', '代码', '算法', '数据', '函数', '类']
        if any(keyword in ' '.join(key_phrases) for keyword in tech_keywords):
            themes.append("编程技术")
        
        # 学习相关主题
        learning_keywords = ['学习', '教育', '知识', '理解', '掌握', '练习']
        if any(keyword in ' '.join(key_phrases) for keyword in learning_keywords):
            themes.append("学习方法")
        
        # 如果没有识别出特定主题，使用通用主题
        if not themes:
            themes = ["一般概念", "基础知识"]
        
        return themes[:5]  # 最多返回5个主题
    
    def _build_concept_hierarchy(self, text: str) -> Dict[str, List[str]]:
        """构建概念层次结构"""
        # 简单的层次结构构建
        hierarchy = {}
        
        # 查找标题和子标题
        lines = text.split('\n')
        current_topic = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测标题（简单规则）
            if line.startswith('#') or len(line) < 50 and line.endswith((':', '：')):
                current_topic = line.replace('#', '').replace(':', '').replace('：', '').strip()
                if current_topic not in hierarchy:
                    hierarchy[current_topic] = []
            elif current_topic and line.startswith(('-', '•', '1.', '2.', '3.')):
                concept = line.lstrip('-•123456789. ').strip()
                if concept and len(concept) < 100:
                    hierarchy[current_topic].append(concept)
        
        return hierarchy
    
    def _analyze_difficulty_distribution(self, text: str) -> Dict[str, int]:
        """分析难度分布"""
        sentences = self._split_sentences(text)
        
        difficulty_counts = {"简单": 0, "中等": 0, "困难": 0}
        
        for sentence in sentences:
            sentence_score = self._calculate_readability(sentence)
            if sentence_score >= 0.6:
                difficulty_counts["简单"] += 1
            elif sentence_score >= 0.3:
                difficulty_counts["中等"] += 1
            else:
                difficulty_counts["困难"] += 1
        
        return difficulty_counts
    
    def _extract_learning_objectives(self, text: str) -> List[str]:
        """提取学习目标"""
        objectives = []
        
        # 查找明确的学习目标表述
        objective_patterns = [
            r'学习目标[：:](.+)',
            r'本章将学习(.+)',
            r'通过本节你将(.+)',
            r'学完本课程你将能够(.+)'
        ]
        
        for pattern in objective_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            objectives.extend([match.strip() for match in matches])
        
        # 如果没有找到明确的学习目标，基于内容推断
        if not objectives:
            key_phrases = self._extract_key_phrases(text, 5)
            objectives = [f"理解{phrase}的概念和应用" for phrase in key_phrases[:3]]
        
        return objectives[:5]
    
    def _identify_prerequisites(self, text: str) -> List[str]:
        """识别前置知识"""
        prerequisites = []
        
        # 查找前置知识的表述
        prereq_patterns = [
            r'前置知识[：:](.+)',
            r'需要掌握(.+)',
            r'建议先学习(.+)',
            r'基于(.+)的基础'
        ]
        
        for pattern in prereq_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            prerequisites.extend([match.strip() for match in matches])
        
        # 基于内容推断前置知识
        if not prerequisites:
            if 'python' in text.lower() or 'Python' in text:
                prerequisites.append("编程基础")
            if '算法' in text:
                prerequisites.append("数学基础")
            if '数据结构' in text:
                prerequisites.append("计算机科学基础")
        
        return prerequisites[:3]
    
    def _suggest_follow_up_topics(self, text: str) -> List[str]:
        """建议后续主题"""
        follow_ups = []
        
        key_phrases = self._extract_key_phrases(text, 10)
        
        # 基于关键词建议后续主题
        for phrase in key_phrases:
            if phrase in ['python', 'Python']:
                follow_ups.extend(["高级Python特性", "Python框架应用"])
            elif phrase in ['算法', 'algorithm']:
                follow_ups.extend(["算法优化", "复杂算法设计"])
            elif phrase in ['数据', 'data']:
                follow_ups.extend(["数据分析", "机器学习"])
        
        # 去重并限制数量
        follow_ups = list(set(follow_ups))
        return follow_ups[:5]

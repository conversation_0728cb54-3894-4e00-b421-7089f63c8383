#!/usr/bin/env python3
"""
Example: Using embedding service with document processing

This example shows how to integrate the embedding service with 
the existing document processing pipeline.
"""

import asyncio
import j<PERSON>
from typing import List, Dict, Any

from app.services.embedding import EmbeddingService
from app.services.document import ChunkService, DocumentService


async def example_document_embedding_workflow():
    """
    Example workflow: Process document -> Generate chunks -> Create embeddings
    """
    print("🚀 Starting document embedding workflow example...\n")
    
    # Step 1: Initialize services
    print("1️⃣ Initializing services...")
    embedding_service = EmbeddingService()
    
    # Check embedding service health
    health = await embedding_service.health_check()
    print(f"   Embedding service status: {health['status']}")
    print(f"   Model: {health.get('model', 'unknown')}")
    print(f"   Dimension: {health.get('dimension', 'unknown')}")
    
    # Step 2: Simulate document chunks
    print("\n2️⃣ Simulating document chunks...")
    
    # These would normally come from the document processing service
    document_chunks = [
        {
            "id": "doc1_chunk1",
            "content": "FastAPI is a modern, fast (high-performance), web framework for building APIs with Python 3.7+ based on standard Python type hints.",
            "metadata": {"document_id": "doc1", "chunk_index": 0}
        },
        {
            "id": "doc1_chunk2", 
            "content": "The key features include: Fast to code, Fast to run, Fewer bugs, Intuitive, Easy, Short, Robust, Standards-based.",
            "metadata": {"document_id": "doc1", "chunk_index": 1}
        },
        {
            "id": "doc1_chunk3",
            "content": "FastAPI provides automatic interactive API documentation using Swagger UI and ReDoc.",
            "metadata": {"document_id": "doc1", "chunk_index": 2}
        }
    ]
    
    print(f"   Generated {len(document_chunks)} chunks")
    
    # Step 3: Generate embeddings for chunks
    print("\n3️⃣ Generating embeddings...")
    
    chunk_ids = [chunk["id"] for chunk in document_chunks]
    chunk_texts = [chunk["content"] for chunk in document_chunks]
    
    try:
        embeddings = await embedding_service.embed_texts(chunk_texts)
        print(f"   ✅ Generated {len(embeddings)} embeddings")
        print(f"   Vector dimension: {len(embeddings[0]) if embeddings else 0}")
        
        # Step 4: Combine chunks with embeddings
        print("\n4️⃣ Combining chunks with embeddings...")
        
        enriched_chunks = []
        for i, chunk in enumerate(document_chunks):
            enriched_chunk = {
                **chunk,
                "embedding": embeddings[i],
                "embedding_model": embedding_service.get_model_name(),
                "embedding_dimension": len(embeddings[i])
            }
            enriched_chunks.append(enriched_chunk)
        
        print(f"   ✅ Created {len(enriched_chunks)} enriched chunks")
        
        # Step 5: Demonstrate similarity search capability
        print("\n5️⃣ Demonstrating similarity search...")
        
        # Generate embedding for a query
        query = "How to build APIs with Python?"
        query_embedding = await embedding_service.embed_single_text(query)
        
        # Calculate cosine similarity (simplified)
        def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
            """Calculate cosine similarity between two vectors"""
            import math
            
            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            magnitude1 = math.sqrt(sum(a * a for a in vec1))
            magnitude2 = math.sqrt(sum(a * a for a in vec2))
            
            if magnitude1 == 0 or magnitude2 == 0:
                return 0
            
            return dot_product / (magnitude1 * magnitude2)
        
        # Find most similar chunks
        similarities = []
        for chunk in enriched_chunks:
            similarity = cosine_similarity(query_embedding, chunk["embedding"])
            similarities.append({
                "chunk_id": chunk["id"],
                "content": chunk["content"][:100] + "...",
                "similarity": similarity
            })
        
        # Sort by similarity
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        
        print(f"   Query: '{query}'")
        print("   Most similar chunks:")
        for i, sim in enumerate(similarities[:2]):
            print(f"   {i+1}. {sim['chunk_id']} (similarity: {sim['similarity']:.4f})")
            print(f"      Content: {sim['content']}")
        
        # Step 6: Show integration with task system
        print("\n6️⃣ Integration with background tasks...")
        
        # This would normally be done via the task system
        print("   📝 In a real system, you would:")
        print("   - Queue embedding tasks for large documents")
        print("   - Store embeddings in Manticore Search")
        print("   - Index documents for semantic search")
        print("   - Enable RAG (Retrieval-Augmented Generation)")
        
        return enriched_chunks
        
    except Exception as e:
        print(f"   ❌ Error generating embeddings: {e}")
        return None


async def example_batch_processing():
    """
    Example of batch processing multiple documents
    """
    print("\n" + "="*60)
    print("📦 BATCH PROCESSING EXAMPLE")
    print("="*60)
    
    embedding_service = EmbeddingService()
    
    # Simulate multiple documents
    documents = [
        {"id": "doc1", "title": "Python Programming", "content": "Python is a versatile programming language."},
        {"id": "doc2", "title": "Machine Learning", "content": "ML algorithms learn patterns from data."},
        {"id": "doc3", "title": "Web Development", "content": "Building web applications with modern frameworks."},
        {"id": "doc4", "title": "Data Science", "content": "Analyzing and interpreting complex data sets."},
        {"id": "doc5", "title": "AI Research", "content": "Advancing artificial intelligence capabilities."}
    ]
    
    print(f"Processing {len(documents)} documents...")
    
    # Extract texts
    doc_ids = [doc["id"] for doc in documents]
    doc_texts = [f"{doc['title']}: {doc['content']}" for doc in documents]
    
    # Generate embeddings in batch
    try:
        embeddings = await embedding_service.embed_texts(doc_texts)
        
        print(f"✅ Generated embeddings for {len(embeddings)} documents")
        
        # Create document index
        document_index = []
        for i, doc in enumerate(documents):
            document_index.append({
                "id": doc["id"],
                "title": doc["title"],
                "content": doc["content"],
                "embedding": embeddings[i]
            })
        
        print("📊 Document index created successfully")
        return document_index
        
    except Exception as e:
        print(f"❌ Batch processing failed: {e}")
        return None


async def main():
    """Main example function"""
    print("🎯 EMBEDDING SERVICE INTEGRATION EXAMPLES")
    print("="*60)
    
    # Run document workflow example
    enriched_chunks = await example_document_embedding_workflow()
    
    if enriched_chunks:
        print(f"\n✅ Document workflow completed successfully!")
        print(f"   Processed {len(enriched_chunks)} chunks with embeddings")
    
    # Run batch processing example
    document_index = await example_batch_processing()
    
    if document_index:
        print(f"\n✅ Batch processing completed successfully!")
        print(f"   Indexed {len(document_index)} documents")
    
    print("\n🎉 All examples completed!")
    print("\n💡 Next steps:")
    print("   - Integrate with Manticore Search for vector storage")
    print("   - Add semantic search endpoints")
    print("   - Implement RAG (Retrieval-Augmented Generation)")
    print("   - Add embedding caching for performance")


if __name__ == "__main__":
    asyncio.run(main())

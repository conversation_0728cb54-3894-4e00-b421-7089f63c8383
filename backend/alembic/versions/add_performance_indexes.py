"""Add performance indexes

Revision ID: add_performance_indexes
Revises: 
Create Date: 2025-08-16 06:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_performance_indexes'
down_revision = None
depends_on = None


def upgrade():
    """添加性能优化索引"""
    
    # 用户表索引
    op.create_index('idx_user_email', 'user', ['email'], unique=True)
    op.create_index('idx_user_is_active', 'user', ['is_active'])
    
    # 文档表索引
    op.create_index('idx_document_owner_id', 'document', ['owner_id'])
    op.create_index('idx_document_created_at', 'document', ['created_at'])
    op.create_index('idx_document_updated_at', 'document', ['updated_at'])
    op.create_index('idx_document_owner_created', 'document', ['owner_id', 'created_at'])
    op.create_index('idx_document_file_type', 'document', ['file_type'])
    op.create_index('idx_document_title', 'document', ['title'])
    
    # 文档块表索引
    op.create_index('idx_document_chunk_document_id', 'document_chunk', ['document_id'])
    op.create_index('idx_document_chunk_index', 'document_chunk', ['document_id', 'chunk_index'])
    op.create_index('idx_document_chunk_created_at', 'document_chunk', ['created_at'])
    
    # 主题表索引
    op.create_index('idx_topic_owner_id', 'topic', ['owner_id'])
    op.create_index('idx_topic_created_at', 'topic', ['created_at'])
    op.create_index('idx_topic_updated_at', 'topic', ['updated_at'])
    op.create_index('idx_topic_name', 'topic', ['name'])
    op.create_index('idx_topic_owner_created', 'topic', ['owner_id', 'created_at'])
    
    # 对话表索引
    op.create_index('idx_conversation_topic_id', 'conversation', ['topic_id'])
    op.create_index('idx_conversation_created_at', 'conversation', ['created_at'])
    op.create_index('idx_conversation_updated_at', 'conversation', ['updated_at'])
    op.create_index('idx_conversation_topic_created', 'conversation', ['topic_id', 'created_at'])
    
    # 消息表索引（如果存在）
    try:
        op.create_index('idx_message_conversation_id', 'message', ['conversation_id'])
        op.create_index('idx_message_created_at', 'message', ['created_at'])
        op.create_index('idx_message_role', 'message', ['role'])
        op.create_index('idx_message_conv_created', 'message', ['conversation_id', 'created_at'])
    except:
        # 如果消息表不存在，跳过
        pass
    
    # 项目表索引
    op.create_index('idx_item_owner_id', 'item', ['owner_id'])
    op.create_index('idx_item_created_at', 'item', ['created_at'])
    op.create_index('idx_item_updated_at', 'item', ['updated_at'])
    op.create_index('idx_item_title', 'item', ['title'])


def downgrade():
    """删除性能优化索引"""
    
    # 删除用户表索引
    op.drop_index('idx_user_email', table_name='user')
    op.drop_index('idx_user_is_active', table_name='user')
    
    # 删除文档表索引
    op.drop_index('idx_document_owner_id', table_name='document')
    op.drop_index('idx_document_created_at', table_name='document')
    op.drop_index('idx_document_updated_at', table_name='document')
    op.drop_index('idx_document_owner_created', table_name='document')
    op.drop_index('idx_document_file_type', table_name='document')
    op.drop_index('idx_document_title', table_name='document')
    
    # 删除文档块表索引
    op.drop_index('idx_document_chunk_document_id', table_name='document_chunk')
    op.drop_index('idx_document_chunk_index', table_name='document_chunk')
    op.drop_index('idx_document_chunk_created_at', table_name='document_chunk')
    
    # 删除主题表索引
    op.drop_index('idx_topic_owner_id', table_name='topic')
    op.drop_index('idx_topic_created_at', table_name='topic')
    op.drop_index('idx_topic_updated_at', table_name='topic')
    op.drop_index('idx_topic_name', table_name='topic')
    op.drop_index('idx_topic_owner_created', table_name='topic')
    
    # 删除对话表索引
    op.drop_index('idx_conversation_topic_id', table_name='conversation')
    op.drop_index('idx_conversation_created_at', table_name='conversation')
    op.drop_index('idx_conversation_updated_at', table_name='conversation')
    op.drop_index('idx_conversation_topic_created', table_name='conversation')
    
    # 删除消息表索引（如果存在）
    try:
        op.drop_index('idx_message_conversation_id', table_name='message')
        op.drop_index('idx_message_created_at', table_name='message')
        op.drop_index('idx_message_role', table_name='message')
        op.drop_index('idx_message_conv_created', table_name='message')
    except:
        # 如果消息表不存在，跳过
        pass
    
    # 删除项目表索引
    op.drop_index('idx_item_owner_id', table_name='item')
    op.drop_index('idx_item_created_at', table_name='item')
    op.drop_index('idx_item_updated_at', table_name='item')
    op.drop_index('idx_item_title', table_name='item')

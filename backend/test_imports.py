#!/usr/bin/env python3
"""
测试模型导入是否正常工作
"""

def test_basic_imports():
    """测试基础导入"""
    try:
        # 测试现有模型的导入
        from app.models import User, Item, Message, Token
        print("✅ 基础模型导入成功")
        
        # 测试新模型的导入
        from app.models import Document, Topic, Conversation
        print("✅ 新模型导入成功")
        
        # 测试具体的模型类
        print(f"User 类: {User}")
        print(f"Document 类: {Document}")
        print(f"Topic 类: {Topic}")
        print(f"Conversation 类: {Conversation}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_model_attributes():
    """测试模型属性"""
    try:
        from app.models import User, Document
        
        # 检查 User 模型的属性
        user_fields = User.__fields__ if hasattr(User, '__fields__') else User.model_fields
        print(f"User 模型字段: {list(user_fields.keys())}")
        
        # 检查 Document 模型的属性
        doc_fields = Document.__fields__ if hasattr(Document, '__fields__') else Document.model_fields
        print(f"Document 模型字段: {list(doc_fields.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ 模型属性检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试模型导入...")
    
    success1 = test_basic_imports()
    success2 = test_model_attributes()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！模型重构成功！")
    else:
        print("\n❌ 测试失败，需要修复问题")

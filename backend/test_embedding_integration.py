#!/usr/bin/env python3
"""
Test script for embedding service integration

This script tests the embedding service integration with the main system.
"""

import asyncio
import json
import requests
import time
from typing import List, Dict, Any

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_check():
    """Test embedding service health check"""
    print("🔍 Testing embedding service health check...")
    
    response = requests.get(f"{BASE_URL}/embedding/health")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Health check passed: {data['status']}")
        print(f"   Model: {data['model']}")
        print(f"   Dimension: {data['dimension']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_sync_embedding():
    """Test synchronous embedding"""
    print("\n🔍 Testing synchronous embedding...")
    
    payload = {
        "ids": ["test1", "test2", "test3"],
        "texts": [
            "This is a test document about machine learning.",
            "Python is a great programming language.",
            "FastAPI makes building APIs easy and fast."
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/embedding/embed",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Sync embedding successful")
        print(f"   Processed {len(data['results'])} texts")
        
        # Check vector dimensions
        for result in data['results']:
            vector_dim = len(result['vector'])
            print(f"   ID: {result['id']}, Vector dimension: {vector_dim}")
            
        return True
    else:
        print(f"❌ Sync embedding failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def test_batch_embedding():
    """Test asynchronous batch embedding"""
    print("\n🔍 Testing batch embedding...")
    
    payload = {
        "ids": ["batch1", "batch2", "batch3", "batch4"],
        "texts": [
            "Large language models are transforming AI.",
            "Vector databases enable semantic search.",
            "Embedding models convert text to vectors.",
            "RAG systems combine retrieval and generation."
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/embedding/embed/batch",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Batch embedding queued successfully")
        print(f"   Task ID: {data['task_id']}")
        print(f"   Batch size: {data['batch_size']}")
        print(f"   Status: {data['status']}")
        return True
    else:
        print(f"❌ Batch embedding failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def test_api_docs():
    """Test if API documentation includes embedding endpoints"""
    print("\n🔍 Testing API documentation...")
    
    response = requests.get(f"{BASE_URL}/openapi.json")
    
    if response.status_code == 200:
        openapi_spec = response.json()
        paths = openapi_spec.get("paths", {})
        
        embedding_endpoints = [
            "/api/v1/embedding/embed",
            "/api/v1/embedding/embed/batch", 
            "/api/v1/embedding/health"
        ]
        
        found_endpoints = []
        for endpoint in embedding_endpoints:
            if endpoint in paths:
                found_endpoints.append(endpoint)
        
        if len(found_endpoints) == len(embedding_endpoints):
            print(f"✅ All embedding endpoints found in API docs")
            for endpoint in found_endpoints:
                print(f"   - {endpoint}")
            return True
        else:
            print(f"❌ Missing embedding endpoints in API docs")
            print(f"   Found: {found_endpoints}")
            return False
    else:
        print(f"❌ Failed to get API docs: {response.status_code}")
        return False

def test_error_handling():
    """Test error handling"""
    print("\n🔍 Testing error handling...")
    
    # Test mismatched IDs and texts
    payload = {
        "ids": ["id1", "id2"],
        "texts": ["text1"]  # Mismatch: 2 IDs but 1 text
    }
    
    response = requests.post(
        f"{BASE_URL}/embedding/embed",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 400:
        print("✅ Error handling works correctly for mismatched inputs")
        return True
    else:
        print(f"❌ Error handling failed: expected 400, got {response.status_code}")
        return False

def run_all_tests():
    """Run all integration tests"""
    print("🚀 Starting embedding service integration tests...\n")
    
    tests = [
        ("Health Check", test_health_check),
        ("Synchronous Embedding", test_sync_embedding),
        ("Batch Embedding", test_batch_embedding),
        ("API Documentation", test_api_docs),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Embedding service integration is successful.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)

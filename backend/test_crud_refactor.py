#!/usr/bin/env python3
"""
测试 CRUD 重构的完整性和正确性
"""

def test_crud_imports():
    """测试 CRUD 导入"""
    try:
        # 测试现有的 CRUD 导入方式
        from app import crud
        print("✅ CRUD 模块导入成功")
        
        # 测试具体的 CRUD 函数
        functions_to_test = [
            'create_user', 'update_user', 'get_user_by_email', 'authenticate',
            'create_item', 'get_item', 'update_item', 'delete_item',
            'create_document', 'get_document', 'update_document', 'delete_document',
            'create_topic', 'get_topic', 'update_topic', 'delete_topic',
            'create_conversation', 'get_conversation', 'update_conversation'
        ]
        
        for func_name in functions_to_test:
            if hasattr(crud, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数缺失")
                return False
                
        return True
    except Exception as e:
        print(f"❌ CRUD 导入失败: {e}")
        return False

def test_direct_crud_imports():
    """测试直接 CRUD 导入"""
    try:
        # 测试直接从 crud 模块导入
        from app.crud import create_user, create_item, create_document, create_topic
        print("✅ 直接 CRUD 函数导入成功")
        
        # 测试 CRUD 类导入
        from app.crud import user, item, document, topic, conversation
        print("✅ CRUD 实例导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 直接 CRUD 导入失败: {e}")
        return False

def test_crud_function_signatures():
    """测试 CRUD 函数签名"""
    try:
        import inspect
        from app.crud import create_user, create_item, authenticate
        
        # 检查 create_user 函数签名
        sig = inspect.signature(create_user)
        params = list(sig.parameters.keys())
        assert 'session' in params, "create_user 应该有 session 参数"
        assert 'user_create' in params, "create_user 应该有 user_create 参数"
        print("✅ create_user 函数签名正确")
        
        # 检查 create_item 函数签名
        sig = inspect.signature(create_item)
        params = list(sig.parameters.keys())
        assert 'session' in params, "create_item 应该有 session 参数"
        assert 'item_in' in params, "create_item 应该有 item_in 参数"
        assert 'owner_id' in params, "create_item 应该有 owner_id 参数"
        print("✅ create_item 函数签名正确")
        
        # 检查 authenticate 函数签名
        sig = inspect.signature(authenticate)
        params = list(sig.parameters.keys())
        assert 'session' in params, "authenticate 应该有 session 参数"
        assert 'email' in params, "authenticate 应该有 email 参数"
        assert 'password' in params, "authenticate 应该有 password 参数"
        print("✅ authenticate 函数签名正确")
        
        return True
    except Exception as e:
        print(f"❌ 函数签名检查失败: {e}")
        return False

def test_new_crud_functions():
    """测试新的 CRUD 函数"""
    try:
        from app.crud import (
            create_document, get_document, update_document, delete_document,
            create_topic, get_topic, update_topic, delete_topic,
            create_conversation, get_conversation, update_conversation
        )
        print("✅ 新的 CRUD 函数导入成功")
        
        # 检查函数是否可调用
        assert callable(create_document), "create_document 应该是可调用的"
        assert callable(create_topic), "create_topic 应该是可调用的"
        assert callable(create_conversation), "create_conversation 应该是可调用的"
        print("✅ 新的 CRUD 函数可调用性检查通过")
        
        return True
    except Exception as e:
        print(f"❌ 新 CRUD 函数测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试 CRUD 重构...")
    print("=" * 50)
    
    tests = [
        test_crud_imports,
        test_direct_crud_imports,
        test_crud_function_signatures,
        test_new_crud_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 CRUD 重构完全成功！")
        print("✅ 所有现有 CRUD 函数保持兼容")
        print("✅ 新 CRUD 函数功能正常")
        print("✅ 函数签名和行为一致")
    else:
        print("❌ 部分测试失败，需要修复")

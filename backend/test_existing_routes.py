#!/usr/bin/env python3
"""
测试现有路由的 CRUD 调用是否仍然正常工作
"""

def test_items_route_imports():
    """测试 items 路由的导入"""
    try:
        # 模拟 items.py 中的导入
        from app.api.deps import CurrentUser, SessionDep
        from app.models import Item, ItemCreate, ItemPublic, ItemsPublic, ItemUpdate, Message
        print("✅ Items 路由模型导入成功")
        
        # 测试 CRUD 导入（items.py 中没有直接导入 crud，而是在函数中调用）
        from app import crud
        
        # 检查关键函数是否存在
        assert hasattr(crud, 'create_item'), "crud.create_item 应该存在"
        print("✅ Items 路由 CRUD 函数可用")
        
        return True
    except Exception as e:
        print(f"❌ Items 路由导入失败: {e}")
        return False

def test_users_route_imports():
    """测试 users 路由的导入"""
    try:
        # 模拟 users.py 中的导入
        from app import crud
        from app.api.deps import (
            CurrentUser,
            SessionDep,
            get_current_active_superuser,
        )
        from app.models import (
            Item,
            Message,
            UpdatePassword,
            User,
            UserCreate,
            UserPublic,
            UsersPublic,
            UserUpdate,
            UserUpdateMe,
        )
        print("✅ Users 路由导入成功")
        
        # 检查关键 CRUD 函数
        assert hasattr(crud, 'create_user'), "crud.create_user 应该存在"
        assert hasattr(crud, 'update_user'), "crud.update_user 应该存在"
        assert hasattr(crud, 'get_user_by_email'), "crud.get_user_by_email 应该存在"
        assert hasattr(crud, 'authenticate'), "crud.authenticate 应该存在"
        print("✅ Users 路由 CRUD 函数可用")
        
        return True
    except Exception as e:
        print(f"❌ Users 路由导入失败: {e}")
        return False

def test_crud_function_compatibility():
    """测试 CRUD 函数兼容性"""
    try:
        import inspect
        from app import crud
        
        # 检查 create_user 函数签名是否与原来一致
        sig = inspect.signature(crud.create_user)
        params = list(sig.parameters.keys())
        
        # 原来的签名: create_user(*, session: Session, user_create: UserCreate) -> User
        assert 'session' in params, "create_user 应该有 session 参数"
        assert 'user_create' in params, "create_user 应该有 user_create 参数"
        print("✅ create_user 函数签名兼容")
        
        # 检查 create_item 函数签名
        sig = inspect.signature(crud.create_item)
        params = list(sig.parameters.keys())
        
        # 原来的签名: create_item(*, session: Session, item_in: ItemCreate, owner_id: uuid.UUID) -> Item
        assert 'session' in params, "create_item 应该有 session 参数"
        assert 'item_in' in params, "create_item 应该有 item_in 参数"
        assert 'owner_id' in params, "create_item 应该有 owner_id 参数"
        print("✅ create_item 函数签名兼容")
        
        return True
    except Exception as e:
        print(f"❌ CRUD 函数兼容性检查失败: {e}")
        return False

def test_new_crud_availability():
    """测试新 CRUD 函数的可用性"""
    try:
        from app.crud import (
            # 文档相关
            create_document, get_document, get_documents_by_owner,
            # 主题相关  
            create_topic, get_topic, get_topics_by_owner,
            # 对话相关
            create_conversation, get_conversation, get_conversations_by_owner
        )
        print("✅ 新 CRUD 函数导入成功")
        
        # 检查函数是否可调用
        assert callable(create_document), "create_document 应该是可调用的"
        assert callable(create_topic), "create_topic 应该是可调用的"
        assert callable(create_conversation), "create_conversation 应该是可调用的"
        print("✅ 新 CRUD 函数可调用")
        
        return True
    except Exception as e:
        print(f"❌ 新 CRUD 函数测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试现有路由兼容性...")
    print("=" * 50)
    
    tests = [
        test_items_route_imports,
        test_users_route_imports,
        test_crud_function_compatibility,
        test_new_crud_availability
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 现有路由完全兼容！")
        print("✅ 所有现有路由的导入正常")
        print("✅ CRUD 函数签名保持一致")
        print("✅ 新功能可以正常使用")
    else:
        print("❌ 部分测试失败，需要修复")

import asyncio
import httpx
import pytest
from fastapi.testclient import TestClient
from app.main import app

# 测试集成后的 LLM 服务
def test_llm_integration():
    """测试 LLM 服务集成到后端系统"""
    client = TestClient(app)
    
    # 测试健康检查
    response = client.get("/api/v1/llm/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "llm-integration"
    
    print("✅ LLM 健康检查通过")


async def test_llm_generate_integration():
    """测试 LLM 生成功能集成"""
    base_url = "http://localhost:8000"
    
    request_data = {
        "conversation_id": "backend-test-001",
        "user_query": "Hello, can you help me with a simple test?",
        "max_tokens": 100,
        "temperature": 0.3
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(
                f"{base_url}/api/v1/llm/generate",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 生成成功!")
                print(f"📄 回答: {result['text'][:100]}...")
                print(f"📊 Token 使用: {result['usage']}")
                print(f"📝 元数据: {result['metadata']}")
                return True
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False


def test_api_structure():
    """测试 API 结构"""
    client = TestClient(app)
    
    # 测试 API 文档
    response = client.get("/docs")
    assert response.status_code == 200
    
    print("✅ API 结构测试通过")


if __name__ == "__main__":
    print("🚀 后端 LLM 集成测试")
    print("=" * 50)
    
    # 运行同步测试
    print("\n📋 测试 1: API 结构")
    test_api_structure()
    
    print("\n📋 测试 2: LLM 健康检查")
    test_llm_integration()
    
    print("\n📋 测试 3: LLM 生成功能")
    print("请确保后端服务正在运行...")
    # asyncio.run(test_llm_generate_integration())
    
    print("\n🎉 集成测试完成！")
    print("💡 提示：运行 'uvicorn app.main:app --reload' 启动后端服务")
    print("💡 然后访问 http://localhost:8000/docs 查看 API 文档")
#!/usr/bin/env python3
"""
Manticore Search集成测试脚本

基于POC验证结果，测试集成到主系统后的Manticore Search功能
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.manticore_client import ManticoreClient, init_manticore_client
from app.services.search.manticore_service import ManticoreSearchService
from app.services.search.search_manager import SearchManager, init_search_manager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_manticore_client():
    """测试基础Manticore客户端功能"""
    logger.info("🚀 测试基础Manticore客户端功能")
    
    try:
        # 初始化客户端
        client = await init_manticore_client()
        
        # 测试连接
        async with client as c:
            health = await c.health_check()
            logger.info(f"✅ 健康检查: {'通过' if health else '失败'}")
            
            if not health:
                logger.error("❌ Manticore服务器连接失败")
                return False
            
            # 创建测试表
            table_name = "integration_test"
            schema = {
                "id": {"type": "bigint"},
                "title": {"type": "text"},
                "content": {"type": "text"},
                "embedding": {"type": "float_vector(1536)"}
            }
            
            # 删除可能存在的表
            try:
                await c.execute_sql(f"DROP TABLE IF EXISTS {table_name}")
            except:
                pass
            
            # 创建新表
            success = await c.create_table(table_name, schema)
            logger.info(f"✅ 创建表 '{table_name}': {'成功' if success else '失败'}")
            
            if success:
                # 插入测试文档
                test_doc = {
                    "id": 1,
                    "title": "人工智能基础",
                    "content": "人工智能是计算机科学的重要分支，包括机器学习、深度学习等技术。"
                }
                
                insert_success = await c.insert_document(table_name, test_doc)
                logger.info(f"✅ 插入文档: {'成功' if insert_success else '失败'}")
                
                # 搜索测试
                if insert_success:
                    results = await c.search(table_name, "人工智能", limit=5)
                    hits = results.get("hits", [])
                    logger.info(f"✅ 搜索结果: 找到 {len(hits)} 条记录")
                    
                    for hit in hits:
                        logger.info(f"   - 文档ID: {hit.get('_id')}, 分数: {hit.get('_score')}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_search_service():
    """测试搜索服务功能"""
    logger.info("🔍 测试搜索服务功能")
    
    try:
        # 创建搜索服务
        search_service = ManticoreSearchService()
        
        # 创建文档表
        table_name = "service_test_docs"
        success = await search_service.create_documents_table(table_name)
        logger.info(f"✅ 创建文档表: {'成功' if success else '失败'}")
        
        if success:
            # 索引测试文档
            test_docs = [
                {
                    "id": 1,
                    "title": "机器学习入门",
                    "content": "机器学习是人工智能的核心技术，通过算法让计算机从数据中学习。",
                    "source": "test_doc_1.txt"
                },
                {
                    "id": 2,
                    "title": "深度学习原理",
                    "content": "深度学习使用神经网络模型，能够处理复杂的模式识别任务。",
                    "source": "test_doc_2.txt"
                },
                {
                    "id": 3,
                    "title": "自然语言处理",
                    "content": "自然语言处理让计算机能够理解和生成人类语言。",
                    "source": "test_doc_3.txt"
                }
            ]
            
            # 批量索引文档
            indexed_count = 0
            for doc in test_docs:
                success = await search_service.index_document(
                    table_name=table_name,
                    doc_id=doc["id"],
                    title=doc["title"],
                    content=doc["content"],
                    source=doc["source"]
                )
                if success:
                    indexed_count += 1
            
            logger.info(f"✅ 索引文档: {indexed_count}/{len(test_docs)} 成功")
            
            # 测试全文搜索
            search_results = await search_service.search_documents(
                table_name=table_name,
                query="机器学习",
                limit=10
            )
            
            hits = search_results.get("hits", [])
            logger.info(f"✅ 全文搜索 '机器学习': 找到 {len(hits)} 条结果")
            
            for hit in hits:
                source = hit.get("_source", {})
                score = hit.get("_score", 0)
                logger.info(f"   - 标题: {source.get('title')}, 分数: {score}")
            
            # 测试统计信息
            stats = await search_service.get_table_stats(table_name)
            logger.info(f"✅ 表统计信息: {stats}")
            
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"❌ 搜索服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_search_manager():
    """测试搜索管理器功能"""
    logger.info("🎯 测试搜索管理器功能")
    
    try:
        # 初始化搜索管理器
        search_manager = await init_search_manager()
        
        # 健康检查
        health = await search_manager.health_check()
        logger.info(f"✅ 搜索管理器健康检查: {health}")
        
        if not health.get("overall"):
            logger.warning("⚠️ 搜索管理器健康检查未完全通过，但继续测试")
        
        # 测试文档索引（不使用embedding，因为可能没有配置OpenAI API）
        test_success = True
        try:
            # 直接使用Manticore服务进行测试
            manticore_service = search_manager.manticore
            
            # 创建测试表
            table_name = "manager_test_docs"
            success = await manticore_service.create_documents_table(table_name)
            logger.info(f"✅ 管理器创建表: {'成功' if success else '失败'}")
            
            if success:
                # 索引测试文档（不使用embedding）
                success = await manticore_service.index_document(
                    table_name=table_name,
                    doc_id=1,
                    title="测试文档",
                    content="这是一个测试文档，用于验证搜索管理器的功能。",
                    source="test.txt"
                )
                logger.info(f"✅ 管理器索引文档: {'成功' if success else '失败'}")
                
                if success:
                    # 测试搜索
                    results = await manticore_service.search_documents(
                        table_name=table_name,
                        query="测试",
                        limit=5
                    )
                    
                    hits = results.get("hits", [])
                    logger.info(f"✅ 管理器搜索测试: 找到 {len(hits)} 条结果")
                    
                    for hit in hits:
                        source = hit.get("_source", {})
                        score = hit.get("_score", 0)
                        logger.info(f"   - 内容: {source.get('content')[:50]}..., 分数: {score}")
        
        except Exception as e:
            logger.warning(f"⚠️ 搜索管理器部分功能测试失败: {e}")
            test_success = False
        
        return test_success
        
    except Exception as e:
        logger.error(f"❌ 搜索管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_configuration():
    """测试配置"""
    logger.info("⚙️ 测试配置")
    
    logger.info(f"✅ Manticore主机: {settings.MANTICORE_HOST}")
    logger.info(f"✅ Manticore端口: {settings.MANTICORE_PORT}")
    logger.info(f"✅ Manticore协议: {settings.MANTICORE_SCHEME}")
    logger.info(f"✅ Manticore URL: {settings.MANTICORE_URL}")
    
    return True


async def main():
    """主测试函数"""
    logger.info("🎉 开始Manticore Search集成测试")
    
    # 测试结果
    results = {
        "配置测试": False,
        "客户端测试": False,
        "搜索服务测试": False,
        "搜索管理器测试": False
    }
    
    # 运行测试
    results["配置测试"] = await test_configuration()
    results["客户端测试"] = await test_manticore_client()
    results["搜索服务测试"] = await test_search_service()
    results["搜索管理器测试"] = await test_search_manager()
    
    # 输出结果
    logger.info("\n" + "="*50)
    logger.info("📊 测试结果汇总")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    if all_passed:
        logger.info("🎉 所有测试通过！Manticore Search集成成功！")
    else:
        logger.info("⚠️ 部分测试失败，请检查配置和服务状态")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())

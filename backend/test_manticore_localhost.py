#!/usr/bin/env python3
"""
Manticore Search集成测试脚本 - 使用localhost连接

测试集成到主系统后的Manticore Search功能，使用localhost连接
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.manticore_client import ManticoreClient
from app.services.search.manticore_service import ManticoreSearchService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_localhost_connection():
    """测试localhost连接"""
    logger.info("🚀 测试localhost连接到Manticore")
    
    try:
        # 使用localhost连接
        client = ManticoreClient(host="localhost", port=9308, scheme="http")
        
        async with client as c:
            # 测试连接
            health = await c.health_check()
            logger.info(f"✅ 健康检查: {'通过' if health else '失败'}")
            
            if not health:
                logger.error("❌ 无法连接到Manticore服务器")
                return False
            
            # 创建测试表
            table_name = "localhost_test"
            schema = {
                "id": {"type": "bigint"},
                "title": {"type": "text"},
                "content": {"type": "text"}
            }
            
            # 删除可能存在的表
            try:
                await c.execute_sql(f"DROP TABLE IF EXISTS {table_name}")
            except:
                pass
            
            # 创建新表
            success = await c.create_table(table_name, schema)
            logger.info(f"✅ 创建表: {'成功' if success else '失败'}")
            
            if success:
                # 插入测试文档
                test_doc = {
                    "id": 1,
                    "title": "集成测试文档",
                    "content": "这是一个用于验证Manticore Search集成的测试文档。包含人工智能、机器学习等关键词。"
                }
                
                insert_success = await c.insert_document(table_name, test_doc)
                logger.info(f"✅ 插入文档: {'成功' if insert_success else '失败'}")
                
                if insert_success:
                    # 搜索测试
                    results = await c.search(table_name, "人工智能", limit=5)
                    hits = results.get("hits", [])
                    logger.info(f"✅ 搜索结果: 找到 {len(hits)} 条记录")
                    
                    for hit in hits:
                        source = hit.get("_source", {})
                        score = hit.get("_score", 0)
                        logger.info(f"   - 标题: {source.get('title')}, 分数: {score}")
                    
                    # 测试SQL查询
                    sql_result = await c.execute_sql(f"SELECT COUNT(*) as total FROM {table_name}")
                    logger.info(f"✅ SQL查询结果: {sql_result}")
                    
                    return True
            
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_search_service_localhost():
    """测试搜索服务 - localhost"""
    logger.info("🔍 测试搜索服务 - localhost")
    
    try:
        # 创建使用localhost的搜索服务
        client = ManticoreClient(host="localhost", port=9308, scheme="http")
        search_service = ManticoreSearchService(client)
        
        # 创建文档表
        table_name = "service_localhost_test"
        success = await search_service.create_documents_table(table_name)
        logger.info(f"✅ 创建文档表: {'成功' if success else '失败'}")
        
        if success:
            # 索引测试文档
            test_docs = [
                {
                    "id": 1,
                    "title": "深度学习基础",
                    "content": "深度学习是机器学习的一个分支，使用多层神经网络来学习数据的表示。",
                    "source": "dl_basics.txt"
                },
                {
                    "id": 2,
                    "title": "自然语言处理应用",
                    "content": "自然语言处理技术广泛应用于搜索引擎、聊天机器人和文本分析。",
                    "source": "nlp_apps.txt"
                }
            ]
            
            # 批量索引文档
            indexed_count = 0
            for doc in test_docs:
                success = await search_service.index_document(
                    table_name=table_name,
                    doc_id=doc["id"],
                    title=doc["title"],
                    content=doc["content"],
                    source=doc["source"]
                )
                if success:
                    indexed_count += 1
            
            logger.info(f"✅ 索引文档: {indexed_count}/{len(test_docs)} 成功")
            
            if indexed_count > 0:
                # 测试搜索
                search_results = await search_service.search_documents(
                    table_name=table_name,
                    query="深度学习",
                    limit=10
                )
                
                hits = search_results.get("hits", [])
                logger.info(f"✅ 搜索 '深度学习': 找到 {len(hits)} 条结果")
                
                for hit in hits:
                    source = hit.get("_source", {})
                    score = hit.get("_score", 0)
                    logger.info(f"   - 标题: {source.get('title')}, 分数: {score}")
                
                # 测试另一个查询
                search_results2 = await search_service.search_documents(
                    table_name=table_name,
                    query="自然语言",
                    limit=10
                )
                
                hits2 = search_results2.get("hits", [])
                logger.info(f"✅ 搜索 '自然语言': 找到 {len(hits2)} 条结果")
                
                return True
        
        return False
        
    except Exception as e:
        logger.error(f"❌ 搜索服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("🎉 开始Manticore Search集成测试 - localhost版本")
    
    # 测试结果
    results = {
        "基础连接测试": False,
        "搜索服务测试": False
    }
    
    # 运行测试
    results["基础连接测试"] = await test_localhost_connection()
    results["搜索服务测试"] = await test_search_service_localhost()
    
    # 输出结果
    logger.info("\n" + "="*50)
    logger.info("📊 测试结果汇总")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    if all_passed:
        logger.info("🎉 所有测试通过！Manticore Search集成成功！")
        logger.info("💡 提示: 在生产环境中，请确保正确配置Manticore主机名")
    else:
        logger.info("⚠️ 部分测试失败，请检查Manticore服务状态")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())

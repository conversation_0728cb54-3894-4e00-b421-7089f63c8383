#!/usr/bin/env python3
"""
完整功能测试脚本

测试数据库迁移、模型、CRUD、服务、API 和任务的完整功能
"""

import subprocess
import sys
import os


def run_command(command: str, description: str) -> bool:
    """运行命令并返回是否成功"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        print(f"✓ {description} 成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败")
        print(f"错误代码: {e.returncode}")
        if e.stdout:
            print("标准输出:")
            print(e.stdout)
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False


def test_database_migration() -> bool:
    """测试数据库迁移"""
    print("\n" + "="*60)
    print("测试数据库迁移")
    print("="*60)
    
    # 检查迁移文件是否存在
    migration_files = subprocess.run(
        "ls app/alembic/versions/ | grep -E '.*_add_document_and_conversation_models.py'",
        shell=True,
        capture_output=True,
        text=True
    )
    
    if migration_files.returncode == 0:
        print("✓ 迁移文件已创建")
        print(f"迁移文件: {migration_files.stdout.strip()}")
        return True
    else:
        print("✗ 迁移文件未找到")
        return False


def test_imports() -> bool:
    """测试所有模块导入"""
    print("\n" + "="*60)
    print("测试模块导入")
    print("="*60)
    
    imports_to_test = [
        "from app.models import Document, DocumentChunk, Topic, Conversation",
        "from app.crud.document import create_document, get_document",
        "from app.services.document import DocumentService, ChunkService, ProcessingService",
        "from app.api.routes.documents import router",
        "from app.tasks import process_document_task, reprocess_document_task",
    ]
    
    for import_statement in imports_to_test:
        success = run_command(
            f'uv run python -c "{import_statement}; print(\\"Import successful: {import_statement}\\")"',
            f"导入测试: {import_statement}"
        )
        if not success:
            return False
    
    return True


def test_unit_tests() -> bool:
    """运行单元测试"""
    print("\n" + "="*60)
    print("运行单元测试")
    print("="*60)
    
    test_commands = [
        ("uv run pytest app/tests/crud/test_document.py -v", "CRUD 测试"),
        ("uv run pytest app/tests/services/test_document_service.py -v", "服务层测试"),
        ("uv run pytest app/tests/test_tasks.py -v", "任务测试"),
    ]
    
    for command, description in test_commands:
        success = run_command(command, description)
        if not success:
            print(f"⚠ {description} 失败，但继续执行其他测试")
    
    return True


def test_api_tests() -> bool:
    """运行 API 测试"""
    print("\n" + "="*60)
    print("运行 API 测试")
    print("="*60)
    
    success = run_command(
        "uv run pytest app/tests/api/routes/test_documents.py -v",
        "文档 API 测试"
    )
    
    if not success:
        print("⚠ API 测试失败，可能需要数据库连接")
    
    return True


def test_integration() -> bool:
    """运行集成测试"""
    print("\n" + "="*60)
    print("运行集成测试")
    print("="*60)
    
    # 测试完整的工作流程
    integration_test = '''
import uuid
from app.services.document import DocumentService, ChunkService, ProcessingService
from app.models import DocumentCreate
from sqlmodel import Session
from app.core.db import engine

# 创建测试文档
with Session(engine) as session:
    service = DocumentService(session)
    doc_in = DocumentCreate(
        title="Integration Test Doc",
        content="This is integration test content. " * 20,
        file_type="txt",
        size=500
    )
    
    # 创建文档
    document = service.create_document(doc_in, uuid.uuid4(), auto_process=False)
    print(f"Created document: {document.id}")
    
    # 测试分块服务
    chunk_service = ChunkService(session)
    chunks = chunk_service.split_document(document)
    print(f"Created {len(chunks)} chunks")
    
    # 测试处理服务
    processing_service = ProcessingService(session)
    status = processing_service.get_processing_status(str(document.id))
    print(f"Processing status: {status['status']}")
    
    print("Integration test completed successfully!")
'''
    
    success = run_command(
        f'uv run python -c "{integration_test}"',
        "集成测试"
    )
    
    if not success:
        print("⚠ 集成测试失败，可能需要数据库连接")
    
    return True


def main():
    """主测试函数"""
    print("="*80)
    print("完整功能测试套件")
    print("="*80)
    
    tests = [
        ("数据库迁移", test_database_migration),
        ("模块导入", test_imports),
        ("单元测试", test_unit_tests),
        ("API 测试", test_api_tests),
        ("集成测试", test_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} 执行时发生异常: {e}")
            results[test_name] = False
    
    # 输出总结
    print("\n" + "="*80)
    print("测试结果总结")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:20} : {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据库迁移和测试套件创建成功。")
        return True
    else:
        print("⚠ 部分测试失败，请检查具体错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

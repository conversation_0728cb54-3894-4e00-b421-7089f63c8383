#!/usr/bin/env python3
"""
核心功能测试脚本

测试核心功能而不依赖外部服务
"""

import uuid
import sys
import traceback


def test_model_imports():
    """测试模型导入"""
    print("测试模型导入...")
    try:
        from app.models import (
            Document, DocumentCreate, DocumentUpdate, DocumentPublic,
            DocumentChunk, DocumentChunkCreate, DocumentChunkPublic,
            Topic, TopicCreate, Conversation, ConversationCreate
        )
        print("✓ 所有模型导入成功")
        return True
    except Exception as e:
        print(f"✗ 模型导入失败: {e}")
        return False


def test_crud_imports():
    """测试 CRUD 导入"""
    print("测试 CRUD 导入...")
    try:
        from app.crud.document import (
            create_document, get_document, get_documents_by_owner,
            update_document, delete_document,
            create_document_chunk, get_document_chunks
        )
        print("✓ 所有 CRUD 函数导入成功")
        return True
    except Exception as e:
        print(f"✗ CRUD 导入失败: {e}")
        return False


def test_service_imports():
    """测试服务导入"""
    print("测试服务导入...")
    try:
        from app.services.document import DocumentService, ChunkService, ProcessingService
        print("✓ 所有服务类导入成功")
        return True
    except Exception as e:
        print(f"✗ 服务导入失败: {e}")
        return False


def test_api_imports():
    """测试 API 导入"""
    print("测试 API 导入...")
    try:
        from app.api.routes.documents import router
        from app.api.deps import DocumentServiceDep, ChunkServiceDep, ProcessingServiceDep
        print("✓ API 路由和依赖注入导入成功")
        return True
    except Exception as e:
        print(f"✗ API 导入失败: {e}")
        return False


def test_task_imports():
    """测试任务导入"""
    print("测试任务导入...")
    try:
        from app.tasks import (
            process_document_task, reprocess_document_task,
            enqueue_document_processing, enqueue_document_reprocessing
        )
        print("✓ 所有任务函数导入成功")
        return True
    except Exception as e:
        print(f"✗ 任务导入失败: {e}")
        return False


def test_engine_integration():
    """测试引擎集成"""
    print("测试引擎集成...")
    try:
        from engines.text_splitter import TextSplitterEngine
        from engines.text_splitter.models import Document as EngineDocument
        from engines.text_splitter.strategies import TokenBasedStrategy
        
        # 创建引擎实例
        engine = TextSplitterEngine()
        
        # 创建测试文档
        test_doc = EngineDocument(
            title="Test Document",
            content="This is a test document. " * 20,
            file_type="txt",
            size=400
        )
        
        # 测试分割
        strategy = TokenBasedStrategy(max_tokens=50)
        result = engine.split_document(test_doc, strategy)
        
        print(f"✓ 引擎集成成功，分割出 {result.total_chunks} 个块")
        return True
    except Exception as e:
        print(f"✗ 引擎集成失败: {e}")
        traceback.print_exc()
        return False


def test_model_creation():
    """测试模型创建"""
    print("测试模型创建...")
    try:
        from app.models import DocumentCreate, DocumentChunkCreate
        
        # 创建文档模型
        doc_create = DocumentCreate(
            title="Test Document",
            content="This is test content",
            file_type="txt",
            size=20
        )
        
        # 创建文档块模型
        chunk_create = DocumentChunkCreate(
            document_id=uuid.uuid4(),
            content="Test chunk content",
            chunk_index=0,
            start_char=0,
            end_char=18,
            token_count=3
        )
        
        print("✓ 模型创建成功")
        return True
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return False


def test_service_instantiation():
    """测试服务实例化"""
    print("测试服务实例化...")
    try:
        from app.services.document import ProcessingService
        
        # 创建处理服务实例（不传入 session）
        service = ProcessingService()
        
        print("✓ 服务实例化成功")
        return True
    except Exception as e:
        print(f"✗ 服务实例化失败: {e}")
        return False


def test_api_router():
    """测试 API 路由器"""
    print("测试 API 路由器...")
    try:
        from fastapi import APIRouter
        from app.api.routes.documents import router as documents_router
        
        # 创建主路由器并包含文档路由
        main_router = APIRouter()
        main_router.include_router(documents_router)
        
        # 检查路由数量
        routes = list(main_router.routes)
        print(f"✓ API 路由器创建成功，包含 {len(routes)} 个路由")
        return True
    except Exception as e:
        print(f"✗ API 路由器测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("="*60)
    print("核心功能测试")
    print("="*60)
    
    tests = [
        ("模型导入", test_model_imports),
        ("CRUD 导入", test_crud_imports),
        ("服务导入", test_service_imports),
        ("API 导入", test_api_imports),
        ("任务导入", test_task_imports),
        ("引擎集成", test_engine_integration),
        ("模型创建", test_model_creation),
        ("服务实例化", test_service_instantiation),
        ("API 路由器", test_api_router),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} 执行时发生异常: {e}")
            results[test_name] = False
    
    # 输出总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:15} : {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        return True
    else:
        print("⚠ 部分测试失败，请检查具体错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

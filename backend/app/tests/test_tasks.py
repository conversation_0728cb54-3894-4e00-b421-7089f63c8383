import uuid
from sqlmodel import Session

from app.tasks import (
    process_document_task,
    reprocess_document_task,
    process_document_with_strategy_task,
    enqueue_document_processing,
    enqueue_document_reprocessing,
    enqueue_document_processing_with_strategy,
)
from app.tests.utils.document import create_random_document


def test_process_document_task(db: Session) -> None:
    """Test process_document_task function."""
    document = create_random_document(db)
    
    # Call the task function directly (not through Dramatiq)
    result = process_document_task(str(document.id))
    
    assert result["success"] is True
    assert result["document_id"] == str(document.id)
    assert "chunks_created" in result


def test_process_document_task_not_found() -> None:
    """Test process_document_task with non-existent document."""
    fake_id = str(uuid.uuid4())
    
    result = process_document_task(fake_id)
    
    assert result["success"] is False
    assert result["document_id"] == fake_id
    assert "error" in result


def test_reprocess_document_task(db: Session) -> None:
    """Test reprocess_document_task function."""
    document = create_random_document(db)
    
    result = reprocess_document_task(str(document.id))
    
    assert result["success"] is True
    assert result["document_id"] == str(document.id)


def test_process_document_with_strategy_task(db: Session) -> None:
    """Test process_document_with_strategy_task function."""
    document = create_random_document(db)
    strategy_config = {
        "type": "token",
        "max_tokens": 100,
        "model_name": "gpt-3.5-turbo"
    }
    
    result = process_document_with_strategy_task(str(document.id), strategy_config)
    
    assert result["success"] is True
    assert result["document_id"] == str(document.id)
    assert result["strategy_config"] == strategy_config


def test_process_document_with_invalid_strategy(db: Session) -> None:
    """Test process_document_with_strategy_task with invalid strategy."""
    document = create_random_document(db)
    strategy_config = {
        "type": "invalid_strategy"
    }
    
    result = process_document_with_strategy_task(str(document.id), strategy_config)
    
    assert result["success"] is False
    assert "error" in result


def test_enqueue_document_processing(db: Session) -> None:
    """Test enqueue_document_processing function."""
    document = create_random_document(db)
    
    # This should return a Dramatiq message object
    message = enqueue_document_processing(str(document.id))
    
    assert message is not None
    assert hasattr(message, 'message_id')


def test_enqueue_document_reprocessing(db: Session) -> None:
    """Test enqueue_document_reprocessing function."""
    document = create_random_document(db)
    
    message = enqueue_document_reprocessing(str(document.id))
    
    assert message is not None
    assert hasattr(message, 'message_id')


def test_enqueue_document_processing_with_strategy(db: Session) -> None:
    """Test enqueue_document_processing_with_strategy function."""
    document = create_random_document(db)
    strategy_config = {
        "type": "character",
        "max_chars": 500
    }
    
    message = enqueue_document_processing_with_strategy(
        str(document.id), strategy_config
    )
    
    assert message is not None
    assert hasattr(message, 'message_id')

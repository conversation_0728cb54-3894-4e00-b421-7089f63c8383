import uuid
from sqlmodel import Session

from app import crud
from app.models import Document, DocumentCreate, DocumentChunk, DocumentChunkCreate
from app.tests.utils.user import create_random_user
from app.tests.utils.utils import random_lower_string


def create_random_document(db: Session, *, owner_id: uuid.UUID | None = None) -> Document:
    """Create a random document for testing."""
    if owner_id is None:
        user = create_random_user(db)
        owner_id = user.id
    
    title = random_lower_string()
    content = f"This is test content for document {title}. " * 10  # Make it longer
    file_type = "txt"
    size = len(content.encode('utf-8'))
    
    document_in = DocumentCreate(
        title=title,
        content=content,
        file_type=file_type,
        size=size
    )
    
    return crud.create_document(session=db, document_in=document_in, owner_id=owner_id)


def create_random_document_chunk(
    db: Session, 
    *, 
    document_id: uuid.UUID | None = None
) -> DocumentChunk:
    """Create a random document chunk for testing."""
    if document_id is None:
        document = create_random_document(db)
        document_id = document.id
    
    content = f"This is a test chunk content. {random_lower_string()}"
    chunk_index = 0
    start_char = 0
    end_char = len(content)
    token_count = len(content.split())
    
    chunk_in = DocumentChunkCreate(
        document_id=document_id,
        content=content,
        chunk_index=chunk_index,
        start_char=start_char,
        end_char=end_char,
        token_count=token_count
    )
    
    return crud.create_document_chunk(session=db, chunk_in=chunk_in)


def create_document_with_chunks(
    db: Session, 
    *, 
    owner_id: uuid.UUID | None = None,
    num_chunks: int = 3
) -> tuple[Document, list[DocumentChunk]]:
    """Create a document with multiple chunks for testing."""
    document = create_random_document(db, owner_id=owner_id)
    
    chunks = []
    for i in range(num_chunks):
        content = f"Chunk {i}: {random_lower_string()} content for testing."
        start_char = i * 50
        end_char = start_char + len(content)
        
        chunk_in = DocumentChunkCreate(
            document_id=document.id,
            content=content,
            chunk_index=i,
            start_char=start_char,
            end_char=end_char,
            token_count=len(content.split())
        )
        
        chunk = crud.create_document_chunk(session=db, chunk_in=chunk_in)
        chunks.append(chunk)
    
    return document, chunks

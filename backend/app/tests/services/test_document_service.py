import uuid
from sqlmodel import Session

from app.models import DocumentCreate, DocumentUpdate
from app.services.document import DocumentService, ChunkService, ProcessingService
from app.tests.utils.document import create_random_document
from app.tests.utils.user import create_random_user
from app.tests.utils.utils import random_lower_string


def test_document_service_create(db: Session) -> None:
    """Test DocumentService create method."""
    user = create_random_user(db)
    service = DocumentService(db)
    
    document_in = DocumentCreate(
        title="Test Document",
        content="This is test content",
        file_type="txt",
        size=20
    )
    
    document = service.create_document(
        document_in=document_in,
        owner_id=user.id,
        auto_process=False  # Disable auto processing for test
    )
    
    assert document.title == document_in.title
    assert document.content == document_in.content
    assert document.owner_id == user.id


def test_document_service_get(db: Session) -> None:
    """Test DocumentService get method."""
    document = create_random_document(db)
    service = DocumentService(db)
    
    retrieved_document = service.get_document(document.id)
    
    assert retrieved_document is not None
    assert retrieved_document.id == document.id
    assert retrieved_document.title == document.title


def test_document_service_get_user_documents(db: Session) -> None:
    """Test DocumentService get_user_documents method."""
    user = create_random_user(db)
    service = DocumentService(db)
    
    # Create documents for the user
    doc1 = create_random_document(db, owner_id=user.id)
    doc2 = create_random_document(db, owner_id=user.id)
    
    documents = service.get_user_documents(owner_id=user.id, skip=0, limit=100)
    
    assert len(documents) >= 2
    document_ids = [doc.id for doc in documents]
    assert doc1.id in document_ids
    assert doc2.id in document_ids


def test_document_service_update(db: Session) -> None:
    """Test DocumentService update method."""
    document = create_random_document(db)
    service = DocumentService(db)
    
    new_title = random_lower_string()
    document_update = DocumentUpdate(title=new_title)
    
    updated_document = service.update_document(
        document_id=document.id,
        document_in=document_update,
        reprocess=False
    )
    
    assert updated_document is not None
    assert updated_document.title == new_title
    assert updated_document.id == document.id


def test_document_service_delete(db: Session) -> None:
    """Test DocumentService delete method."""
    document = create_random_document(db)
    service = DocumentService(db)
    
    deleted_document = service.delete_document(document.id)
    
    assert deleted_document is not None
    assert deleted_document.id == document.id
    
    # Verify document is deleted
    retrieved_document = service.get_document(document.id)
    assert retrieved_document is None


def test_chunk_service_split_document(db: Session) -> None:
    """Test ChunkService split_document method."""
    document = create_random_document(db)
    service = ChunkService(db)
    
    chunks = service.split_document(document)
    
    assert len(chunks) > 0
    for chunk in chunks:
        assert chunk.document_id == document.id
        assert chunk.content is not None
        assert chunk.chunk_index >= 0


def test_chunk_service_get_document_chunks(db: Session) -> None:
    """Test ChunkService get_document_chunks method."""
    document = create_random_document(db)
    service = ChunkService(db)
    
    # First split the document to create chunks
    created_chunks = service.split_document(document)
    
    # Then retrieve the chunks
    retrieved_chunks = service.get_document_chunks(document.id)
    
    assert len(retrieved_chunks) == len(created_chunks)
    for chunk in retrieved_chunks:
        assert chunk.document_id == document.id


def test_chunk_service_get_chunk_statistics(db: Session) -> None:
    """Test ChunkService get_chunk_statistics method."""
    document = create_random_document(db)
    service = ChunkService(db)
    
    # Create chunks
    chunks = service.split_document(document)
    
    stats = service.get_chunk_statistics(document.id)
    
    assert stats["total_chunks"] == len(chunks)
    assert stats["total_characters"] > 0
    assert stats["avg_chunk_size"] > 0


def test_processing_service_process_document(db: Session) -> None:
    """Test ProcessingService process_document method."""
    document = create_random_document(db)
    service = ProcessingService(db)
    
    result = service.process_document(str(document.id))
    
    assert result["success"] is True
    assert result["document_id"] == str(document.id)
    assert "chunks_created" in result


def test_processing_service_get_processing_status(db: Session) -> None:
    """Test ProcessingService get_processing_status method."""
    document = create_random_document(db)
    service = ProcessingService(db)
    
    status = service.get_processing_status(str(document.id))
    
    assert status["document_id"] == str(document.id)
    assert status["document_title"] == document.title
    assert "status" in status


def test_document_service_prepare_for_splitting(db: Session) -> None:
    """Test DocumentService prepare_for_splitting method."""
    document = create_random_document(db)
    service = DocumentService(db)
    
    engine_doc = service.prepare_for_splitting(document)
    
    assert engine_doc.id == str(document.id)
    assert engine_doc.title == document.title
    assert engine_doc.content == document.content
    assert engine_doc.file_type == document.file_type


def test_document_service_get_split_strategy(db: Session) -> None:
    """Test DocumentService get_split_strategy method."""
    document = create_random_document(db)
    service = DocumentService(db)
    
    strategy = service.get_split_strategy(document)
    
    assert strategy is not None
    assert hasattr(strategy, 'name')

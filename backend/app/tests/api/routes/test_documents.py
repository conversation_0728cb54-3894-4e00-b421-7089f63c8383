import uuid

from fastapi.testclient import Test<PERSON>lient
from sqlmodel import Session

from app.core.config import settings
from app.tests.utils.document import create_random_document, create_document_with_chunks


def test_create_document(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test creating a document."""
    data = {
        "title": "Test Document",
        "content": "This is a test document content.",
        "file_type": "txt",
        "size": 32
    }
    response = client.post(
        f"{settings.API_V1_STR}/documents/",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["title"] == data["title"]
    assert content["content"] == data["content"]
    assert content["file_type"] == data["file_type"]
    assert "id" in content
    assert "owner_id" in content


def test_read_document(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test reading a document."""
    document = create_random_document(db)
    response = client.get(
        f"{settings.API_V1_STR}/documents/{document.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["title"] == document.title
    assert content["content"] == document.content
    assert content["id"] == str(document.id)
    assert content["owner_id"] == str(document.owner_id)


def test_read_document_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test reading a non-existent document."""
    response = client.get(
        f"{settings.API_V1_STR}/documents/{uuid.uuid4()}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404
    content = response.json()
    assert content["detail"] == "Document not found"


def test_read_document_not_enough_permissions(
    client: TestClient, normal_user_token_headers: dict[str, str], db: Session
) -> None:
    """Test reading a document without permissions."""
    document = create_random_document(db)
    response = client.get(
        f"{settings.API_V1_STR}/documents/{document.id}",
        headers=normal_user_token_headers,
    )
    assert response.status_code == 400
    content = response.json()
    assert content["detail"] == "Not enough permissions"


def test_read_documents(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test reading documents list."""
    create_random_document(db)
    create_random_document(db)
    response = client.get(
        f"{settings.API_V1_STR}/documents/",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert len(content["data"]) >= 2


def test_update_document(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test updating a document."""
    document = create_random_document(db)
    data = {
        "title": "Updated Document Title",
        "content": "Updated document content"
    }
    response = client.put(
        f"{settings.API_V1_STR}/documents/{document.id}",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["title"] == data["title"]
    assert content["content"] == data["content"]
    assert content["id"] == str(document.id)


def test_update_document_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test updating a non-existent document."""
    data = {"title": "Updated Title", "content": "Updated content"}
    response = client.put(
        f"{settings.API_V1_STR}/documents/{uuid.uuid4()}",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 404
    content = response.json()
    assert content["detail"] == "Document not found"


def test_delete_document(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test deleting a document."""
    document = create_random_document(db)
    response = client.delete(
        f"{settings.API_V1_STR}/documents/{document.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Document deleted successfully"


def test_delete_document_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    """Test deleting a non-existent document."""
    response = client.delete(
        f"{settings.API_V1_STR}/documents/{uuid.uuid4()}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404
    content = response.json()
    assert content["detail"] == "Document not found"


def test_read_document_chunks(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test reading document chunks."""
    document, chunks = create_document_with_chunks(db, num_chunks=3)
    response = client.get(
        f"{settings.API_V1_STR}/documents/{document.id}/chunks",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert len(content["data"]) == 3
    assert content["count"] == 3


def test_process_document(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test processing a document."""
    document = create_random_document(db)
    response = client.post(
        f"{settings.API_V1_STR}/documents/{document.id}/process",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Document processing started"


def test_reprocess_document(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test reprocessing a document."""
    document = create_random_document(db)
    response = client.post(
        f"{settings.API_V1_STR}/documents/{document.id}/reprocess",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Document reprocessing started"


def test_get_document_stats(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    """Test getting document statistics."""
    document, chunks = create_document_with_chunks(db, num_chunks=2)
    response = client.get(
        f"{settings.API_V1_STR}/documents/{document.id}/stats",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["document_id"] == str(document.id)
    assert content["document_title"] == document.title
    assert "chunk_statistics" in content
    assert "processing_status" in content

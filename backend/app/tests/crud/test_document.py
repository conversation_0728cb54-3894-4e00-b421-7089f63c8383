import uuid
from sqlmodel import Session

from app import crud
from app.models import DocumentCreate, DocumentUpdate, DocumentChunkCreate
from app.tests.utils.document import create_random_document
from app.tests.utils.user import create_random_user
from app.tests.utils.utils import random_lower_string


def test_create_document(db: Session) -> None:
    """Test creating a document."""
    user = create_random_user(db)
    title = random_lower_string()
    content = "This is test content"
    file_type = "txt"
    size = len(content.encode('utf-8'))
    
    document_in = DocumentCreate(
        title=title,
        content=content,
        file_type=file_type,
        size=size
    )
    
    document = crud.create_document(
        session=db, document_in=document_in, owner_id=user.id
    )
    
    assert document.title == title
    assert document.content == content
    assert document.file_type == file_type
    assert document.size == size
    assert document.owner_id == user.id
    assert document.id is not None


def test_get_document(db: Session) -> None:
    """Test getting a document."""
    document = create_random_document(db)
    stored_document = crud.get_document(session=db, document_id=document.id)
    
    assert stored_document
    assert stored_document.id == document.id
    assert stored_document.title == document.title
    assert stored_document.content == document.content


def test_get_document_not_found(db: Session) -> None:
    """Test getting a non-existent document."""
    document = crud.get_document(session=db, document_id=uuid.uuid4())
    assert document is None


def test_get_documents_by_owner(db: Session) -> None:
    """Test getting documents by owner."""
    user = create_random_user(db)
    document1 = create_random_document(db, owner_id=user.id)
    document2 = create_random_document(db, owner_id=user.id)
    
    documents = crud.get_documents_by_owner(
        session=db, owner_id=user.id, skip=0, limit=100
    )
    
    assert len(documents) >= 2
    document_ids = [doc.id for doc in documents]
    assert document1.id in document_ids
    assert document2.id in document_ids


def test_update_document(db: Session) -> None:
    """Test updating a document."""
    document = create_random_document(db)
    new_title = random_lower_string()
    new_content = "Updated content"
    
    document_update = DocumentUpdate(title=new_title, content=new_content)
    updated_document = crud.update_document(
        session=db, db_document=document, document_in=document_update
    )
    
    assert updated_document.id == document.id
    assert updated_document.title == new_title
    assert updated_document.content == new_content
    assert updated_document.owner_id == document.owner_id


def test_delete_document(db: Session) -> None:
    """Test deleting a document."""
    document = create_random_document(db)
    deleted_document = crud.delete_document(session=db, document_id=document.id)
    
    assert deleted_document
    assert deleted_document.id == document.id
    
    # Verify document is deleted
    stored_document = crud.get_document(session=db, document_id=document.id)
    assert stored_document is None


def test_create_document_chunk(db: Session) -> None:
    """Test creating a document chunk."""
    document = create_random_document(db)
    content = "This is a test chunk"
    chunk_index = 0
    start_char = 0
    end_char = len(content)
    token_count = len(content.split())
    
    chunk_in = DocumentChunkCreate(
        document_id=document.id,
        content=content,
        chunk_index=chunk_index,
        start_char=start_char,
        end_char=end_char,
        token_count=token_count
    )
    
    chunk = crud.create_document_chunk(session=db, chunk_in=chunk_in)
    
    assert chunk.content == content
    assert chunk.chunk_index == chunk_index
    assert chunk.start_char == start_char
    assert chunk.end_char == end_char
    assert chunk.token_count == token_count
    assert chunk.document_id == document.id


def test_get_document_chunks(db: Session) -> None:
    """Test getting document chunks."""
    document = create_random_document(db)
    
    # Create multiple chunks
    for i in range(3):
        chunk_in = DocumentChunkCreate(
            document_id=document.id,
            content=f"Chunk {i} content",
            chunk_index=i,
            start_char=i * 10,
            end_char=(i + 1) * 10,
            token_count=2
        )
        crud.create_document_chunk(session=db, chunk_in=chunk_in)
    
    chunks = crud.get_document_chunks(
        session=db, document_id=document.id, skip=0, limit=100
    )
    
    assert len(chunks) == 3
    # Verify chunks are ordered by chunk_index
    for i, chunk in enumerate(chunks):
        assert chunk.chunk_index == i


def test_delete_document_chunks(db: Session) -> None:
    """Test deleting document chunks."""
    document = create_random_document(db)
    
    # Create chunks
    for i in range(2):
        chunk_in = DocumentChunkCreate(
            document_id=document.id,
            content=f"Chunk {i} content",
            chunk_index=i,
            start_char=i * 10,
            end_char=(i + 1) * 10,
            token_count=2
        )
        crud.create_document_chunk(session=db, chunk_in=chunk_in)
    
    # Delete chunks
    deleted_count = crud.delete_document_chunks(session=db, document_id=document.id)
    assert deleted_count == 2
    
    # Verify chunks are deleted
    chunks = crud.get_document_chunks(session=db, document_id=document.id)
    assert len(chunks) == 0

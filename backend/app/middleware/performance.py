"""
性能监控中间件
监控API响应时间、数据库查询等性能指标
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.cache import get_cache_stats

logger = logging.getLogger(__name__)


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app: ASGIApp, slow_request_threshold: float = 1.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
        self.request_stats = {
            "total_requests": 0,
            "slow_requests": 0,
            "total_response_time": 0.0,
            "max_response_time": 0.0,
            "min_response_time": float('inf')
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并监控性能"""
        start_time = time.time()
        
        # 记录请求开始
        self.request_stats["total_requests"] += 1
        
        # 处理请求
        response = await call_next(request)
        
        # 计算响应时间
        process_time = time.time() - start_time
        
        # 更新统计信息
        self._update_stats(process_time)
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录慢请求
        if process_time > self.slow_request_threshold:
            self.request_stats["slow_requests"] += 1
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path} "
                f"took {process_time:.3f}s"
            )
        
        # 记录请求信息
        logger.info(
            f"{request.method} {request.url.path} "
            f"- {response.status_code} - {process_time:.3f}s"
        )
        
        return response
    
    def _update_stats(self, process_time: float):
        """更新统计信息"""
        self.request_stats["total_response_time"] += process_time
        
        if process_time > self.request_stats["max_response_time"]:
            self.request_stats["max_response_time"] = process_time
        
        if process_time < self.request_stats["min_response_time"]:
            self.request_stats["min_response_time"] = process_time
    
    def get_stats(self) -> dict:
        """获取性能统计信息"""
        total_requests = self.request_stats["total_requests"]
        avg_response_time = (
            self.request_stats["total_response_time"] / total_requests
            if total_requests > 0 else 0
        )
        
        slow_request_rate = (
            self.request_stats["slow_requests"] / total_requests * 100
            if total_requests > 0 else 0
        )
        
        return {
            "total_requests": total_requests,
            "slow_requests": self.request_stats["slow_requests"],
            "slow_request_rate": round(slow_request_rate, 2),
            "avg_response_time": round(avg_response_time, 3),
            "max_response_time": round(self.request_stats["max_response_time"], 3),
            "min_response_time": (
                round(self.request_stats["min_response_time"], 3)
                if self.request_stats["min_response_time"] != float('inf') else 0
            ),
            "cache_stats": get_cache_stats()
        }


class DatabaseQueryMonitor:
    """数据库查询监控器"""
    
    def __init__(self):
        self.query_stats = {
            "total_queries": 0,
            "slow_queries": 0,
            "total_query_time": 0.0,
            "max_query_time": 0.0,
            "queries_by_table": {}
        }
        self.slow_query_threshold = 0.1  # 100ms
    
    def record_query(self, query: str, execution_time: float, table_name: str = None):
        """记录查询执行"""
        self.query_stats["total_queries"] += 1
        self.query_stats["total_query_time"] += execution_time
        
        if execution_time > self.query_stats["max_query_time"]:
            self.query_stats["max_query_time"] = execution_time
        
        if execution_time > self.slow_query_threshold:
            self.query_stats["slow_queries"] += 1
            logger.warning(f"Slow query detected: {query[:100]}... took {execution_time:.3f}s")
        
        # 按表统计
        if table_name:
            if table_name not in self.query_stats["queries_by_table"]:
                self.query_stats["queries_by_table"][table_name] = {
                    "count": 0,
                    "total_time": 0.0
                }
            self.query_stats["queries_by_table"][table_name]["count"] += 1
            self.query_stats["queries_by_table"][table_name]["total_time"] += execution_time
    
    def get_stats(self) -> dict:
        """获取查询统计信息"""
        total_queries = self.query_stats["total_queries"]
        avg_query_time = (
            self.query_stats["total_query_time"] / total_queries
            if total_queries > 0 else 0
        )
        
        slow_query_rate = (
            self.query_stats["slow_queries"] / total_queries * 100
            if total_queries > 0 else 0
        )
        
        return {
            "total_queries": total_queries,
            "slow_queries": self.query_stats["slow_queries"],
            "slow_query_rate": round(slow_query_rate, 2),
            "avg_query_time": round(avg_query_time, 3),
            "max_query_time": round(self.query_stats["max_query_time"], 3),
            "queries_by_table": self.query_stats["queries_by_table"]
        }


# 全局监控实例
performance_monitor = None
db_query_monitor = DatabaseQueryMonitor()


def get_performance_stats() -> dict:
    """获取性能统计信息"""
    stats = {
        "database": db_query_monitor.get_stats(),
        "cache": get_cache_stats()
    }
    
    if performance_monitor:
        stats["api"] = performance_monitor.get_stats()
    
    return stats


def log_performance_summary():
    """记录性能摘要"""
    stats = get_performance_stats()
    
    logger.info("=== Performance Summary ===")
    
    if "api" in stats:
        api_stats = stats["api"]
        logger.info(
            f"API: {api_stats['total_requests']} requests, "
            f"avg {api_stats['avg_response_time']}s, "
            f"{api_stats['slow_request_rate']}% slow"
        )
    
    db_stats = stats["database"]
    logger.info(
        f"DB: {db_stats['total_queries']} queries, "
        f"avg {db_stats['avg_query_time']}s, "
        f"{db_stats['slow_query_rate']}% slow"
    )
    
    cache_stats = stats["cache"]
    logger.info(
        f"Cache: {cache_stats['hit_rate']}% hit rate, "
        f"{cache_stats['total_requests']} requests"
    )


class QueryOptimizationRecommendations:
    """查询优化建议"""
    
    @staticmethod
    def analyze_slow_queries(stats: dict) -> list[str]:
        """分析慢查询并提供优化建议"""
        recommendations = []
        
        db_stats = stats.get("database", {})
        slow_query_rate = db_stats.get("slow_query_rate", 0)
        
        if slow_query_rate > 10:
            recommendations.append(
                "High slow query rate detected. Consider adding database indexes."
            )
        
        queries_by_table = db_stats.get("queries_by_table", {})
        for table, table_stats in queries_by_table.items():
            avg_time = table_stats["total_time"] / table_stats["count"]
            if avg_time > 0.1:  # 100ms
                recommendations.append(
                    f"Table '{table}' has slow queries (avg {avg_time:.3f}s). "
                    f"Consider optimizing queries or adding indexes."
                )
        
        cache_stats = stats.get("cache", {})
        hit_rate = cache_stats.get("hit_rate", 0)
        
        if hit_rate < 50:
            recommendations.append(
                "Low cache hit rate. Consider increasing cache TTL or "
                "caching more frequently accessed data."
            )
        
        return recommendations
    
    @staticmethod
    def get_index_suggestions(table_stats: dict) -> list[str]:
        """根据查询统计提供索引建议"""
        suggestions = []
        
        # 这里可以根据实际的查询模式提供更具体的索引建议
        # 目前提供通用建议
        
        for table, stats in table_stats.items():
            if stats["count"] > 100:  # 频繁查询的表
                suggestions.append(
                    f"Consider adding indexes on frequently queried columns in '{table}' table"
                )
        
        return suggestions

"""
统一错误处理中间件
提供用户友好的错误信息和完善的错误追踪
"""

import logging
import traceback
import uuid
from typing import Union
from datetime import datetime

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

logger = logging.getLogger(__name__)


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self):
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {},
            "errors_by_endpoint": {},
            "recent_errors": []
        }
    
    def track_error(self, error_type: str, endpoint: str, error_id: str, details: dict):
        """追踪错误"""
        self.error_stats["total_errors"] += 1
        
        # 按类型统计
        if error_type not in self.error_stats["errors_by_type"]:
            self.error_stats["errors_by_type"][error_type] = 0
        self.error_stats["errors_by_type"][error_type] += 1
        
        # 按端点统计
        if endpoint not in self.error_stats["errors_by_endpoint"]:
            self.error_stats["errors_by_endpoint"][endpoint] = 0
        self.error_stats["errors_by_endpoint"][endpoint] += 1
        
        # 记录最近的错误
        error_record = {
            "error_id": error_id,
            "error_type": error_type,
            "endpoint": endpoint,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details
        }
        
        self.error_stats["recent_errors"].append(error_record)
        
        # 只保留最近100个错误
        if len(self.error_stats["recent_errors"]) > 100:
            self.error_stats["recent_errors"] = self.error_stats["recent_errors"][-100:]
    
    def get_stats(self) -> dict:
        """获取错误统计"""
        return self.error_stats


# 全局错误追踪器
error_tracker = ErrorTracker()


class ErrorResponse:
    """标准化错误响应"""
    
    @staticmethod
    def create_error_response(
        error_id: str,
        error_type: str,
        message: str,
        details: dict = None,
        status_code: int = 500
    ) -> dict:
        """创建标准化错误响应"""
        return {
            "error": {
                "id": error_id,
                "type": error_type,
                "message": message,
                "details": details or {},
                "timestamp": datetime.utcnow().isoformat()
            }
        }
    
    @staticmethod
    def validation_error_response(error_id: str, validation_errors: list) -> dict:
        """创建验证错误响应"""
        user_friendly_errors = []
        
        for error in validation_errors:
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            error_type = error["type"]
            
            # 转换为用户友好的消息
            if error_type == "missing":
                friendly_message = f"字段 '{field}' 是必需的"
            elif error_type == "string_too_short":
                friendly_message = f"字段 '{field}' 太短"
            elif error_type == "string_too_long":
                friendly_message = f"字段 '{field}' 太长"
            elif error_type == "value_error":
                friendly_message = f"字段 '{field}' 的值无效: {message}"
            elif error_type == "type_error":
                friendly_message = f"字段 '{field}' 的类型不正确"
            else:
                friendly_message = f"字段 '{field}': {message}"
            
            user_friendly_errors.append({
                "field": field,
                "message": friendly_message,
                "type": error_type
            })
        
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="validation_error",
            message="请求数据验证失败",
            details={"validation_errors": user_friendly_errors},
            status_code=422
        )
    
    @staticmethod
    def database_error_response(error_id: str, error: SQLAlchemyError) -> dict:
        """创建数据库错误响应"""
        if isinstance(error, IntegrityError):
            message = "数据完整性错误，可能是重复数据或违反约束"
            details = {"constraint_violation": True}
        else:
            message = "数据库操作失败"
            details = {"database_error": True}
        
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="database_error",
            message=message,
            details=details,
            status_code=500
        )
    
    @staticmethod
    def not_found_response(error_id: str, resource: str = "资源") -> dict:
        """创建404错误响应"""
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="not_found",
            message=f"请求的{resource}不存在",
            details={"resource": resource},
            status_code=404
        )
    
    @staticmethod
    def permission_denied_response(error_id: str) -> dict:
        """创建权限拒绝错误响应"""
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="permission_denied",
            message="您没有权限执行此操作",
            details={"permission_required": True},
            status_code=403
        )
    
    @staticmethod
    def authentication_required_response(error_id: str) -> dict:
        """创建认证要求错误响应"""
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="authentication_required",
            message="请先登录后再访问此资源",
            details={"authentication_required": True},
            status_code=401
        )


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并捕获异常"""
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            return await self.handle_exception(request, exc)
    
    async def handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """处理异常并返回标准化响应"""
        error_id = str(uuid.uuid4())
        endpoint = f"{request.method} {request.url.path}"
        
        # 记录错误详情
        error_details = {
            "url": str(request.url),
            "method": request.method,
            "headers": dict(request.headers),
            "user_agent": request.headers.get("user-agent"),
            "ip": request.client.host if request.client else None
        }
        
        # 根据异常类型处理
        if isinstance(exc, HTTPException):
            response_data = self._handle_http_exception(error_id, exc)
            status_code = exc.status_code
            error_type = f"http_{exc.status_code}"
        elif isinstance(exc, RequestValidationError):
            response_data = ErrorResponse.validation_error_response(error_id, exc.errors())
            status_code = 422
            error_type = "validation_error"
        elif isinstance(exc, ValidationError):
            response_data = ErrorResponse.validation_error_response(error_id, exc.errors())
            status_code = 422
            error_type = "validation_error"
        elif isinstance(exc, SQLAlchemyError):
            response_data = ErrorResponse.database_error_response(error_id, exc)
            status_code = 500
            error_type = "database_error"
        else:
            response_data = self._handle_unexpected_exception(error_id, exc)
            status_code = 500
            error_type = "internal_error"
        
        # 记录错误
        logger.error(
            f"Error {error_id} in {endpoint}: {type(exc).__name__}: {str(exc)}",
            extra={
                "error_id": error_id,
                "exception_type": type(exc).__name__,
                "endpoint": endpoint,
                "traceback": traceback.format_exc()
            }
        )
        
        # 追踪错误
        error_tracker.track_error(error_type, endpoint, error_id, error_details)
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    def _handle_http_exception(self, error_id: str, exc: HTTPException) -> dict:
        """处理HTTP异常"""
        if exc.status_code == 404:
            return ErrorResponse.not_found_response(error_id)
        elif exc.status_code == 401:
            return ErrorResponse.authentication_required_response(error_id)
        elif exc.status_code == 403:
            return ErrorResponse.permission_denied_response(error_id)
        else:
            return ErrorResponse.create_error_response(
                error_id=error_id,
                error_type=f"http_{exc.status_code}",
                message=exc.detail or "请求处理失败",
                status_code=exc.status_code
            )
    
    def _handle_unexpected_exception(self, error_id: str, exc: Exception) -> dict:
        """处理意外异常"""
        logger.error(f"Unexpected error {error_id}: {traceback.format_exc()}")
        
        return ErrorResponse.create_error_response(
            error_id=error_id,
            error_type="internal_error",
            message="服务器内部错误，请稍后重试",
            details={
                "error_type": type(exc).__name__,
                "support_message": f"如果问题持续存在，请联系技术支持并提供错误ID: {error_id}"
            },
            status_code=500
        )


def get_error_stats() -> dict:
    """获取错误统计信息"""
    return error_tracker.get_stats()


def create_custom_http_exception(
    status_code: int,
    message: str,
    error_type: str = None,
    details: dict = None
) -> HTTPException:
    """创建自定义HTTP异常"""
    error_id = str(uuid.uuid4())
    
    response_data = ErrorResponse.create_error_response(
        error_id=error_id,
        error_type=error_type or f"http_{status_code}",
        message=message,
        details=details,
        status_code=status_code
    )
    
    return HTTPException(
        status_code=status_code,
        detail=response_data
    )

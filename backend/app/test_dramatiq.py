"""
Test script for Dramatiq integration.

This script tests the Dramatiq task queue functionality.
"""

import logging
import sys
import time
from typing import Dict, Any

# Import task functions
from tasks import (
    enqueue_hello_world,
    enqueue_slow_task,
    enqueue_important_task,
    hello_world_task,
    slow_task,
    important_task,
    failing_task
)
from dramatiq_config import get_broker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_broker_connection() -> bool:
    """Test Dramatiq broker connection."""
    logger.info("Testing Dramatiq broker connection...")
    try:
        broker = get_broker()
        if broker:
            logger.info("✅ Broker connection test passed")
            return True
        else:
            logger.error("❌ Broker connection test failed - no broker")
            return False
    except Exception as e:
        logger.error(f"❌ Broker connection test error: {e}")
        return False


def test_task_enqueueing() -> bool:
    """Test task enqueueing."""
    logger.info("Testing task enqueueing...")
    try:
        # Test hello world task
        message1 = enqueue_hello_world("Dramatiq Test")
        logger.info(f"Enqueued hello_world_task: {message1.message_id}")
        
        # Test slow task
        message2 = enqueue_slow_task(2)
        logger.info(f"Enqueued slow_task: {message2.message_id}")
        
        # Test important task
        test_data = {"test": "data", "items": [1, 2, 3]}
        message3 = enqueue_important_task(test_data)
        logger.info(f"Enqueued important_task: {message3.message_id}")
        
        logger.info("✅ Task enqueueing test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task enqueueing test error: {e}")
        return False


def test_direct_task_execution() -> bool:
    """Test direct task execution (without worker)."""
    logger.info("Testing direct task execution...")
    try:
        # Test hello world task directly
        result1 = hello_world_task("Direct Test")
        logger.info(f"Direct hello_world_task result: {result1}")
        
        # Test slow task directly (with short duration)
        result2 = slow_task(1)
        logger.info(f"Direct slow_task result: {result2}")
        
        # Test important task directly
        test_data = {"direct": "test", "count": 5}
        result3 = important_task(test_data)
        logger.info(f"Direct important_task result: {result3}")
        
        logger.info("✅ Direct task execution test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Direct task execution test error: {e}")
        return False


def test_task_failure_handling() -> bool:
    """Test task failure handling."""
    logger.info("Testing task failure handling...")
    try:
        # Test successful task
        result_success = failing_task(should_fail=False)
        logger.info(f"Success case result: {result_success}")
        
        # Test failing task (this should raise an exception)
        try:
            result_fail = failing_task(should_fail=True)
            logger.error("❌ Failing task should have raised an exception")
            return False
        except Exception as expected_error:
            logger.info(f"Expected failure caught: {expected_error}")
        
        logger.info("✅ Task failure handling test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task failure handling test error: {e}")
        return False


def test_queue_inspection() -> bool:
    """Test queue inspection capabilities."""
    logger.info("Testing queue inspection...")
    try:
        broker = get_broker()
        
        # Get queue names
        queue_names = [queue.name for queue in broker.queues]
        logger.info(f"Available queues: {queue_names}")
        
        # Enqueue some test tasks
        enqueue_hello_world("Queue Test 1")
        enqueue_hello_world("Queue Test 2")
        
        logger.info("✅ Queue inspection test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Queue inspection test error: {e}")
        return False


def test_redis_connection() -> bool:
    """Test Redis connection directly."""
    logger.info("Testing Redis connection...")
    try:
        import redis
        
        # Connect to Redis using the same configuration as Dramatiq
        redis_client = redis.Redis(host="localhost", port=6379, db=0, decode_responses=True)
        
        # Test basic Redis operations
        redis_client.set("dramatiq_test", "connection_test")
        value = redis_client.get("dramatiq_test")
        
        if value == "connection_test":
            logger.info("✅ Redis connection test passed")
            redis_client.delete("dramatiq_test")  # Cleanup
            return True
        else:
            logger.error("❌ Redis connection test failed - value mismatch")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis connection test error: {e}")
        return False


def run_all_tests():
    """Run all Dramatiq tests."""
    logger.info("Starting Dramatiq integration tests...")
    
    # Track test results
    test_results = []
    
    # Run tests
    test_results.append(test_redis_connection())
    test_results.append(test_broker_connection())
    test_results.append(test_direct_task_execution())
    test_results.append(test_task_failure_handling())
    test_results.append(test_task_enqueueing())
    test_results.append(test_queue_inspection())
    
    # Summary
    passed = sum(test_results)
    total = len(test_results)
    
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed")
        return False


def test_worker_integration():
    """Test integration with actual worker (requires worker to be running)."""
    logger.info("Testing worker integration...")
    logger.info("Note: This test requires a Dramatiq worker to be running")
    
    try:
        # Enqueue some tasks
        logger.info("Enqueueing tasks for worker processing...")
        
        message1 = enqueue_hello_world("Worker Test")
        message2 = enqueue_slow_task(3)
        
        logger.info(f"Enqueued tasks: {message1.message_id}, {message2.message_id}")
        logger.info("Tasks have been enqueued. Check worker logs to see if they are processed.")
        logger.info("✅ Worker integration test completed (check worker logs for actual processing)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Worker integration test error: {e}")
        return False


def test_connection_only():
    """Test only the connection components."""
    logger.info("Testing Dramatiq connections...")
    
    test_results = []
    test_results.append(test_redis_connection())
    test_results.append(test_broker_connection())
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        logger.info("✅ Connection tests passed")
        return True
    else:
        logger.error("❌ Connection tests failed")
        return False


if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--connection-only":
            # Test connections only
            success = test_connection_only()
        elif sys.argv[1] == "--worker-test":
            # Test worker integration
            success = test_worker_integration()
        else:
            logger.error(f"Unknown argument: {sys.argv[1]}")
            logger.info("Usage: python test_dramatiq.py [--connection-only|--worker-test]")
            sys.exit(1)
    else:
        # Run all tests
        success = run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

"""
缓存机制实现
提供Redis缓存和内存缓存支持
"""

import json
import hashlib
import pickle
from typing import Any, Optional, Union, Callable
from functools import wraps
from datetime import datetime, timedelta

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from app.core.config import settings


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        # 初始化Redis连接
        if REDIS_AVAILABLE and hasattr(settings, 'REDIS_URL'):
            try:
                self.redis_client = redis.from_url(settings.REDIS_URL)
                self.redis_client.ping()  # 测试连接
            except Exception as e:
                print(f"Redis connection failed: {e}")
                self.redis_client = None
    
    def _generate_key(self, key: str, prefix: str = "cache") -> str:
        """生成缓存键"""
        return f"{prefix}:{key}"
    
    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        return pickle.dumps(value)
    
    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        return pickle.loads(data)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        cache_key = self._generate_key(key)
        
        # 优先使用Redis
        if self.redis_client:
            try:
                data = self.redis_client.get(cache_key)
                if data:
                    self.cache_stats["hits"] += 1
                    return self._deserialize_value(data)
            except Exception as e:
                print(f"Redis get error: {e}")
        
        # 回退到内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            if entry["expires_at"] is None or entry["expires_at"] > datetime.utcnow():
                self.cache_stats["hits"] += 1
                return entry["value"]
            else:
                # 过期删除
                del self.memory_cache[cache_key]
        
        self.cache_stats["misses"] += 1
        return default
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        cache_key = self._generate_key(key)
        
        # 优先使用Redis
        if self.redis_client:
            try:
                serialized_value = self._serialize_value(value)
                self.redis_client.setex(cache_key, ttl, serialized_value)
                self.cache_stats["sets"] += 1
                return True
            except Exception as e:
                print(f"Redis set error: {e}")
        
        # 回退到内存缓存
        expires_at = datetime.utcnow() + timedelta(seconds=ttl) if ttl > 0 else None
        self.memory_cache[cache_key] = {
            "value": value,
            "expires_at": expires_at
        }
        self.cache_stats["sets"] += 1
        return True
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        cache_key = self._generate_key(key)
        deleted = False
        
        # 从Redis删除
        if self.redis_client:
            try:
                deleted = bool(self.redis_client.delete(cache_key))
            except Exception as e:
                print(f"Redis delete error: {e}")
        
        # 从内存缓存删除
        if cache_key in self.memory_cache:
            del self.memory_cache[cache_key]
            deleted = True
        
        if deleted:
            self.cache_stats["deletes"] += 1
        
        return deleted
    
    def clear(self, pattern: str = "*") -> int:
        """清空缓存"""
        count = 0
        
        # 清空Redis
        if self.redis_client:
            try:
                keys = self.redis_client.keys(f"cache:{pattern}")
                if keys:
                    count += self.redis_client.delete(*keys)
            except Exception as e:
                print(f"Redis clear error: {e}")
        
        # 清空内存缓存
        if pattern == "*":
            count += len(self.memory_cache)
            self.memory_cache.clear()
        else:
            # 简单模式匹配
            keys_to_delete = [k for k in self.memory_cache.keys() if pattern in k]
            for key in keys_to_delete:
                del self.memory_cache[key]
            count += len(keys_to_delete)
        
        return count
    
    def get_stats(self) -> dict:
        """获取缓存统计"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            "hit_rate": round(hit_rate, 2),
            "total_requests": total_requests,
            "redis_available": self.redis_client is not None,
            "memory_cache_size": len(self.memory_cache)
        }


# 全局缓存实例
cache = CacheManager()


def cache_key_from_args(*args, **kwargs) -> str:
    """从函数参数生成缓存键"""
    # 创建参数的哈希值
    key_data = {
        "args": args,
        "kwargs": kwargs
    }
    key_str = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_str.encode()).hexdigest()


def cached(ttl: int = 3600, key_prefix: str = "func"):
    """缓存装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = f"{func.__module__}.{func.__name__}"
            args_key = cache_key_from_args(*args, **kwargs)
            cache_key = f"{key_prefix}:{func_name}:{args_key}"
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        # 添加缓存控制方法
        wrapper.cache_clear = lambda: cache.clear(f"{key_prefix}:{func.__module__}.{func.__name__}:*")
        wrapper.cache_info = lambda: cache.get_stats()
        
        return wrapper
    return decorator


def invalidate_cache_pattern(pattern: str):
    """使缓存模式失效"""
    return cache.clear(pattern)


def get_cache_stats():
    """获取缓存统计信息"""
    return cache.get_stats()


# 预定义的缓存键模式
class CacheKeys:
    """缓存键常量"""
    USER_BY_EMAIL = "user:email:{email}"
    USER_BY_ID = "user:id:{user_id}"
    DOCUMENT_BY_ID = "document:id:{document_id}"
    DOCUMENTS_BY_OWNER = "documents:owner:{owner_id}:skip:{skip}:limit:{limit}"
    DOCUMENT_CHUNKS = "document_chunks:doc:{document_id}"
    TOPIC_BY_ID = "topic:id:{topic_id}"
    TOPICS_BY_OWNER = "topics:owner:{owner_id}"
    CONVERSATION_BY_ID = "conversation:id:{conversation_id}"
    CONVERSATIONS_BY_TOPIC = "conversations:topic:{topic_id}"


# 便捷的缓存函数
def cache_user(user_id: str, user_data: Any, ttl: int = 1800):
    """缓存用户数据"""
    cache.set(CacheKeys.USER_BY_ID.format(user_id=user_id), user_data, ttl)


def get_cached_user(user_id: str) -> Any:
    """获取缓存的用户数据"""
    return cache.get(CacheKeys.USER_BY_ID.format(user_id=user_id))


def cache_document(document_id: str, document_data: Any, ttl: int = 3600):
    """缓存文档数据"""
    cache.set(CacheKeys.DOCUMENT_BY_ID.format(document_id=document_id), document_data, ttl)


def get_cached_document(document_id: str) -> Any:
    """获取缓存的文档数据"""
    return cache.get(CacheKeys.DOCUMENT_BY_ID.format(document_id=document_id))


def invalidate_user_cache(user_id: str):
    """使用户缓存失效"""
    cache.delete(CacheKeys.USER_BY_ID.format(user_id=user_id))


def invalidate_document_cache(document_id: str):
    """使文档缓存失效"""
    cache.delete(CacheKeys.DOCUMENT_BY_ID.format(document_id=document_id))

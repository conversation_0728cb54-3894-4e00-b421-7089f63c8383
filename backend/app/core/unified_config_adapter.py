"""
Backend配置适配器

将统一配置适配到backend的配置格式
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from scripts.config.unified_config import get_unified_settings
    
    def get_backend_config():
        """获取backend配置"""
        settings = get_unified_settings()
        
        return {
            "PROJECT_NAME": settings.project_name,
            "ENVIRONMENT": settings.environment,
            "SECRET_KEY": settings.secret_key,
            "ACCESS_TOKEN_EXPIRE_MINUTES": settings.access_token_expire_minutes,
            "FRONTEND_HOST": settings.frontend_host,
            "BACKEND_CORS_ORIGINS": settings.backend_cors_origins,
            "FIRST_SUPERUSER": settings.first_superuser,
            "FIRST_SUPERUSER_PASSWORD": settings.first_superuser_password,
            
            # 数据库配置
            "POSTGRES_SERVER": settings.postgres_server,
            "POSTGRES_PORT": settings.postgres_port,
            "POSTGRES_DB": settings.postgres_db,
            "POSTGRES_USER": settings.postgres_user,
            "POSTGRES_PASSWORD": settings.postgres_password,
            
            # 向量化配置
            "EMBEDDING_OPENAI_API_KEY": settings.embedding_openai_api_key,
            "EMBEDDING_OPENAI_BASE_URL": settings.embedding_openai_base_url,
            "EMBEDDING_DEFAULT_MODEL": settings.embedding_default_model,
            "EMBEDDING_DIM": settings.embedding_dim,
            "EMBEDDING_BATCH_SIZE": settings.embedding_batch_size,
            
            # Manticore配置
            "MANTICORE_HOST": settings.manticore_host,
            "MANTICORE_HTTP_PORT": settings.manticore_http_port,
            "MANTICORE_MYSQL_PORT": settings.manticore_mysql_port,
            
            # LLM配置
            "LLM_OPENAI_API_KEY": settings.llm_openai_api_key,
            "LLM_OPENAI_BASE_URL": settings.llm_openai_base_url,
            "LLM_OPENAI_MODEL": settings.llm_openai_model,
            "LLM_MAX_TOKENS": settings.llm_max_tokens,
            "LLM_TEMPERATURE": settings.llm_temperature,
            
            # 邮件配置
            "SMTP_HOST": settings.smtp_host,
            "SMTP_PORT": settings.smtp_port,
            "SMTP_TLS": settings.smtp_tls,
            "SMTP_SSL": settings.smtp_ssl,
            "SMTP_USER": settings.smtp_user,
            "SMTP_PASSWORD": settings.smtp_password,
            "EMAILS_FROM_EMAIL": settings.emails_from_email,
            "EMAILS_FROM_NAME": settings.emails_from_name,
        }
        
except ImportError:
    def get_backend_config():
        """降级配置"""
        return {}

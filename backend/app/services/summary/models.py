"""
简化的摘要服务数据模型（用于 POC 测试）
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class SummaryType(str, Enum):
    """摘要类型"""
    DOCUMENT = "document"
    CONVERSATION = "conversation"
    TOPIC = "topic"
    LEARNING_SESSION = "learning_session"


class SummaryStrategy(str, Enum):
    """摘要策略"""
    EXTRACTIVE = "extractive"  # 抽取式
    ABSTRACTIVE = "abstractive"  # 生成式
    HYBRID = "hybrid"  # 混合式


class KnowledgePointType(str, Enum):
    """知识点类型"""
    CONCEPT = "concept"  # 概念
    PRINCIPLE = "principle"  # 原理
    METHOD = "method"  # 方法
    EXAMPLE = "example"  # 示例
    APPLICATION = "application"  # 应用


# 基础模型
class SummaryBase(BaseModel):
    """摘要基础模型"""
    title: str = Field(max_length=255)
    content: str
    summary_type: SummaryType
    strategy: SummaryStrategy = SummaryStrategy.HYBRID
    source_id: uuid.UUID  # 源内容ID
    metadata: Optional[Dict[str, Any]] = None


class SummaryCreate(SummaryBase):
    """创建摘要请求"""
    pass


class SummaryUpdate(BaseModel):
    """更新摘要请求"""
    title: Optional[str] = Field(default=None, max_length=255)
    content: Optional[str] = None
    strategy: Optional[SummaryStrategy] = None
    metadata: Optional[Dict[str, Any]] = None


class Summary(SummaryBase):
    """摘要模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class SummaryPublic(SummaryBase):
    """摘要公开模型"""
    id: uuid.UUID
    user_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# 知识点模型
class KnowledgePointBase(BaseModel):
    """知识点基础模型"""
    name: str = Field(max_length=255)
    description: str
    point_type: KnowledgePointType
    importance_score: float = Field(ge=0.0, le=1.0)
    difficulty_level: int = Field(ge=1, le=5)
    related_concepts: Optional[List[str]] = None
    examples: Optional[List[str]] = None


class KnowledgePointCreate(KnowledgePointBase):
    """创建知识点请求"""
    pass


class KnowledgePoint(KnowledgePointBase):
    """知识点模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    summary_id: uuid.UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)


class KnowledgePointPublic(KnowledgePointBase):
    """知识点公开模型"""
    id: uuid.UUID
    summary_id: uuid.UUID
    created_at: datetime


# 学习报告模型
class LearningReport(BaseModel):
    """学习报告"""
    user_id: uuid.UUID
    period_start: datetime
    period_end: datetime
    total_sessions: int
    total_duration: int  # 秒
    documents_studied: int
    conversations_completed: int
    knowledge_points_learned: int
    mastery_improvement: Dict[str, float]
    strengths: List[str]
    areas_for_improvement: List[str]
    recommendations: List[str]
    progress_score: float = Field(ge=0.0, le=1.0)


# API 请求/响应模型
class SummarizeRequest(BaseModel):
    """摘要生成请求"""
    content: str
    summary_type: SummaryType
    strategy: SummaryStrategy = SummaryStrategy.HYBRID
    max_length: int = Field(default=500, ge=50, le=2000)
    include_keywords: bool = True
    extract_knowledge_points: bool = True


class SummarizeResponse(BaseModel):
    """摘要生成响应"""
    summary: str
    keywords: List[str] = []
    knowledge_points: List[KnowledgePointPublic] = []
    reading_time: int  # 预估阅读时间（秒）
    complexity_score: float = Field(ge=0.0, le=1.0)
    metadata: Dict[str, Any] = {}


class ConversationSummaryRequest(BaseModel):
    """对话摘要请求"""
    conversation_id: uuid.UUID
    include_learning_progress: bool = True
    highlight_key_insights: bool = True


class ConversationSummaryResponse(BaseModel):
    """对话摘要响应"""
    summary: str
    key_topics: List[str]
    learning_outcomes: List[str]
    questions_discussed: List[str]
    knowledge_gaps_identified: List[str]
    next_steps: List[str]
    engagement_metrics: Dict[str, Any]


class LearningReportRequest(BaseModel):
    """学习报告请求"""
    user_id: uuid.UUID
    period_days: int = Field(default=7, ge=1, le=365)
    include_detailed_analysis: bool = True
    include_recommendations: bool = True


class BatchSummaryRequest(BaseModel):
    """批量摘要请求"""
    source_ids: List[uuid.UUID]
    summary_type: SummaryType
    strategy: SummaryStrategy = SummaryStrategy.HYBRID
    max_length_per_item: int = Field(default=300, ge=50, le=1000)


class BatchSummaryResponse(BaseModel):
    """批量摘要响应"""
    summaries: List[SummaryPublic]
    total_processed: int
    processing_time: float
    failed_items: List[uuid.UUID] = []


# 文本分析结果模型
class TextAnalysis(BaseModel):
    """文本分析结果"""
    word_count: int
    sentence_count: int
    paragraph_count: int
    reading_time: int  # 秒
    readability_score: float
    complexity_level: str
    language: str = "zh"
    key_phrases: List[str] = []
    sentiment_score: Optional[float] = None


class ContentInsights(BaseModel):
    """内容洞察"""
    main_themes: List[str]
    concept_hierarchy: Dict[str, List[str]]
    difficulty_distribution: Dict[str, int]
    learning_objectives: List[str]
    prerequisite_knowledge: List[str]
    follow_up_topics: List[str]

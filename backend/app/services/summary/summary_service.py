"""
摘要服务业务逻辑
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

try:
    import httpx
except ImportError:
    httpx = None

from .models import (
    Summary, SummaryCreate, SummaryUpdate, SummaryType, SummaryStrategy,
    KnowledgePoint, KnowledgePointCreate, KnowledgePointType, KnowledgePointPublic,
    SummarizeRequest, SummarizeResponse, LearningReport,
    ConversationSummaryRequest, ConversationSummaryResponse,
    TextAnalysis, ContentInsights
)
from .text_processor import TextProcessor


class SummaryService:
    """摘要管理服务"""
    
    def __init__(self):
        self.summaries: Dict[uuid.UUID, Summary] = {}  # 模拟数据库
        self.knowledge_points: Dict[uuid.UUID, List[KnowledgePoint]] = {}
        self.text_processor = TextProcessor()
    
    def create_summary(self, user_id: uuid.UUID, summary_in: SummaryCreate) -> Summary:
        """创建摘要"""
        summary = Summary(
            **summary_in.model_dump(),
            id=uuid.uuid4(),
            user_id=user_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.summaries[summary.id] = summary
        self.knowledge_points[summary.id] = []
        
        return summary
    
    def get_summary(self, summary_id: uuid.UUID) -> Optional[Summary]:
        """获取摘要"""
        return self.summaries.get(summary_id)
    
    def update_summary(self, summary_id: uuid.UUID, update_data: SummaryUpdate) -> Optional[Summary]:
        """更新摘要"""
        summary = self.summaries.get(summary_id)
        if not summary:
            return None
        
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(summary, field, value)
        
        summary.updated_at = datetime.utcnow()
        return summary
    
    def add_knowledge_point(self, summary_id: uuid.UUID, kp_in: KnowledgePointCreate) -> Optional[KnowledgePoint]:
        """添加知识点"""
        if summary_id not in self.knowledge_points:
            return None
        
        knowledge_point = KnowledgePoint(
            **kp_in.model_dump(),
            id=uuid.uuid4(),
            summary_id=summary_id,
            created_at=datetime.utcnow()
        )
        
        self.knowledge_points[summary_id].append(knowledge_point)
        return knowledge_point
    
    def get_knowledge_points(self, summary_id: uuid.UUID) -> List[KnowledgePoint]:
        """获取知识点"""
        return self.knowledge_points.get(summary_id, [])


class TextSummarizationService:
    """文本摘要生成服务"""
    
    def __init__(self):
        self.text_processor = TextProcessor()
        self.llm_service = LLMService()
    
    async def generate_summary(self, request: SummarizeRequest) -> SummarizeResponse:
        """生成摘要"""
        # 1. 分析文本
        analysis = self.text_processor.analyze_text(request.content)
        
        # 2. 提取内容洞察
        insights = self.text_processor.extract_content_insights(request.content)
        
        # 3. 生成摘要
        if request.strategy == SummaryStrategy.EXTRACTIVE:
            summary = self._extractive_summary(request.content, request.max_length)
        elif request.strategy == SummaryStrategy.ABSTRACTIVE:
            summary = await self._abstractive_summary(request.content, request.max_length)
        else:  # HYBRID
            summary = await self._hybrid_summary(request.content, request.max_length)
        
        # 4. 提取关键词
        keywords = analysis.key_phrases if request.include_keywords else []
        
        # 5. 提取知识点
        knowledge_points = []
        if request.extract_knowledge_points:
            knowledge_points = self._extract_knowledge_points(request.content, insights)
        
        return SummarizeResponse(
            summary=summary,
            keywords=keywords,
            knowledge_points=knowledge_points,
            reading_time=analysis.reading_time,
            complexity_score=analysis.readability_score,
            metadata={
                "word_count": analysis.word_count,
                "sentence_count": analysis.sentence_count,
                "language": analysis.language,
                "main_themes": insights.main_themes
            }
        )
    
    def _extractive_summary(self, text: str, max_length: int) -> str:
        """抽取式摘要"""
        sentences = self.text_processor._split_sentences(text)
        
        # 简单的句子评分（基于位置和长度）
        scored_sentences = []
        for i, sentence in enumerate(sentences):
            score = 1.0
            # 开头和结尾的句子权重更高
            if i < len(sentences) * 0.2 or i > len(sentences) * 0.8:
                score += 0.5
            # 中等长度的句子权重更高
            if 10 <= len(sentence.split()) <= 30:
                score += 0.3
            
            scored_sentences.append((sentence, score))
        
        # 按评分排序并选择最佳句子
        scored_sentences.sort(key=lambda x: x[1], reverse=True)
        
        summary_sentences = []
        current_length = 0
        
        for sentence, _ in scored_sentences:
            if current_length + len(sentence) <= max_length:
                summary_sentences.append(sentence)
                current_length += len(sentence)
            else:
                break
        
        return " ".join(summary_sentences)
    
    async def _abstractive_summary(self, text: str, max_length: int) -> str:
        """生成式摘要"""
        try:
            summary = await self.llm_service.generate_summary(text, max_length)
            return summary
        except Exception as e:
            print(f"Abstractive summary failed: {e}")
            # 回退到抽取式摘要
            return self._extractive_summary(text, max_length)
    
    async def _hybrid_summary(self, text: str, max_length: int) -> str:
        """混合式摘要"""
        # 先生成抽取式摘要
        extractive = self._extractive_summary(text, max_length // 2)
        
        # 再基于抽取式摘要生成更精炼的版本
        try:
            refined = await self.llm_service.refine_summary(extractive, max_length)
            return refined
        except Exception as e:
            print(f"Hybrid summary failed: {e}")
            return extractive
    
    def _extract_knowledge_points(self, text: str, insights: ContentInsights) -> List[KnowledgePointPublic]:
        """提取知识点"""
        knowledge_points = []

        # 基于主题提取概念类知识点
        for theme in insights.main_themes:
            kp = KnowledgePointPublic(
                id=uuid.uuid4(),
                summary_id=uuid.uuid4(),  # 临时ID
                name=theme,
                description=f"关于{theme}的核心概念",
                point_type=KnowledgePointType.CONCEPT,
                importance_score=0.8,
                difficulty_level=2,
                created_at=datetime.utcnow()
            )
            knowledge_points.append(kp)

        # 基于学习目标提取方法类知识点
        for objective in insights.learning_objectives:
            kp = KnowledgePointPublic(
                id=uuid.uuid4(),
                summary_id=uuid.uuid4(),  # 临时ID
                name=objective,
                description=f"学习目标：{objective}",
                point_type=KnowledgePointType.METHOD,
                importance_score=0.7,
                difficulty_level=3,
                created_at=datetime.utcnow()
            )
            knowledge_points.append(kp)

        return knowledge_points[:5]  # 限制数量


class LLMService:
    """LLM 集成服务"""
    
    def __init__(self, api_base: str = "http://localhost:8000"):
        self.api_base = api_base
    
    async def generate_summary(self, text: str, max_length: int) -> str:
        """生成摘要"""
        try:
            if httpx is None:
                raise Exception("httpx not available")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/api/v1/llm/summarize",
                    json={
                        "text": text,
                        "max_length": max_length,
                        "language": "zh"
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("summary", "")
                else:
                    raise Exception(f"API error: {response.status_code}")
                    
        except Exception as e:
            print(f"LLM summary generation error: {e}")
            # 返回模拟摘要
            return f"这是关于以下内容的摘要：{text[:100]}..."
    
    async def refine_summary(self, summary: str, max_length: int) -> str:
        """精炼摘要"""
        try:
            if len(summary) <= max_length:
                return summary
            
            # 简单的截断和精炼
            refined = summary[:max_length]
            last_period = refined.rfind('。')
            if last_period > max_length * 0.8:
                refined = refined[:last_period + 1]
            
            return refined
            
        except Exception as e:
            print(f"Summary refinement error: {e}")
            return summary[:max_length]


class ConversationSummaryService:
    """对话摘要服务"""
    
    def __init__(self):
        self.text_summarization = TextSummarizationService()
    
    async def summarize_conversation(self, request: ConversationSummaryRequest) -> ConversationSummaryResponse:
        """总结对话"""
        # 模拟获取对话内容
        conversation_content = self._get_conversation_content(request.conversation_id)
        
        # 生成摘要
        summary_request = SummarizeRequest(
            content=conversation_content,
            summary_type=SummaryType.CONVERSATION,
            max_length=300
        )
        
        summary_response = await self.text_summarization.generate_summary(summary_request)
        
        # 分析对话特征
        key_topics = self._extract_key_topics(conversation_content)
        learning_outcomes = self._identify_learning_outcomes(conversation_content)
        questions_discussed = self._extract_questions(conversation_content)
        knowledge_gaps = self._identify_knowledge_gaps(conversation_content)
        next_steps = self._suggest_next_steps(conversation_content)
        
        return ConversationSummaryResponse(
            summary=summary_response.summary,
            key_topics=key_topics,
            learning_outcomes=learning_outcomes,
            questions_discussed=questions_discussed,
            knowledge_gaps_identified=knowledge_gaps,
            next_steps=next_steps,
            engagement_metrics={
                "total_messages": 10,
                "user_questions": 5,
                "topics_covered": len(key_topics),
                "engagement_score": 0.8
            }
        )
    
    def _get_conversation_content(self, conversation_id: uuid.UUID) -> str:
        """获取对话内容（模拟）"""
        return """
        用户：什么是 Python？
        助手：Python 是一种高级编程语言，以其简洁的语法和强大的功能而闻名。
        用户：Python 有什么特点？
        助手：Python 的主要特点包括：1. 语法简洁易读 2. 跨平台支持 3. 丰富的库生态系统...
        """
    
    def _extract_key_topics(self, content: str) -> List[str]:
        """提取关键主题"""
        return ["Python基础", "编程语言特性", "语法结构"]
    
    def _identify_learning_outcomes(self, content: str) -> List[str]:
        """识别学习成果"""
        return ["理解Python的基本概念", "掌握Python的主要特点"]
    
    def _extract_questions(self, content: str) -> List[str]:
        """提取讨论的问题"""
        return ["什么是Python？", "Python有什么特点？"]
    
    def _identify_knowledge_gaps(self, content: str) -> List[str]:
        """识别知识缺口"""
        return ["Python实际应用场景", "Python与其他语言的对比"]
    
    def _suggest_next_steps(self, content: str) -> List[str]:
        """建议下一步学习"""
        return ["学习Python基础语法", "尝试编写简单的Python程序", "了解Python的应用领域"]

"""
Embedding service data models
"""

from typing import List
from pydantic import BaseModel, Field


class EmbedItem(BaseModel):
    """Single embedding result item"""
    id: str = Field(..., description="Unique identifier for the text")
    vector: List[float] = Field(..., description="Embedding vector")


class EmbedRequest(BaseModel):
    """Request model for embedding API"""
    ids: List[str] = Field(..., description="List of unique identifiers")
    texts: List[str] = Field(..., description="List of texts to embed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "ids": ["chunk_1", "chunk_2"],
                "texts": ["This is the first text.", "This is the second text."]
            }
        }


class EmbedResponse(BaseModel):
    """Response model for embedding API"""
    results: List[EmbedItem] = Field(..., description="List of embedding results")
    
    class Config:
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "id": "chunk_1",
                        "vector": [0.1, 0.2, 0.3]
                    }
                ]
            }
        }


class BatchEmbedResponse(BaseModel):
    """Response model for batch embedding API"""
    status: str = Field(..., description="Task status")
    task_id: str = Field(..., description="Task identifier")
    batch_size: int = Field(..., description="Number of texts in batch")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "queued",
                "task_id": "abc123",
                "batch_size": 5
            }
        }

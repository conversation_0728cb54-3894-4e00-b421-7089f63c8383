"""
简化的对话服务数据模型（用于 POC 测试）
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class MessageRole(str, Enum):
    """消息角色"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ConversationStatus(str, Enum):
    """对话状态"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class LearningLevel(str, Enum):
    """学习水平"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


# 基础模型
class ConversationBase(BaseModel):
    """对话基础模型"""
    title: str = Field(max_length=255)
    topic_id: Optional[uuid.UUID] = None
    status: ConversationStatus = ConversationStatus.ACTIVE
    learning_level: LearningLevel = LearningLevel.BEGINNER
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationCreate(ConversationBase):
    """创建对话请求"""
    pass


class ConversationUpdate(BaseModel):
    """更新对话请求"""
    title: Optional[str] = Field(default=None, max_length=255)
    status: Optional[ConversationStatus] = None
    learning_level: Optional[LearningLevel] = None
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class Conversation(ConversationBase):
    """对话模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ConversationPublic(ConversationBase):
    """对话公开模型"""
    id: uuid.UUID
    user_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# 消息模型
class ConversationMessageBase(BaseModel):
    """对话消息基础模型"""
    role: MessageRole
    content: str
    context_used: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationMessageCreate(ConversationMessageBase):
    """创建消息请求"""
    pass


class ConversationMessage(ConversationMessageBase):
    """对话消息模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    conversation_id: uuid.UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ConversationMessagePublic(ConversationMessageBase):
    """对话消息公开模型"""
    id: uuid.UUID
    conversation_id: uuid.UUID
    created_at: datetime


# 学习进度模型
class LearningProgress(BaseModel):
    """学习进度"""
    knowledge_points_covered: List[str] = []
    mastery_levels: Dict[str, float] = {}
    questions_asked: int = 0
    correct_answers: int = 0
    session_duration: int = 0  # 秒
    engagement_score: float = 0.0


# API 请求/响应模型
class ChatRequest(BaseModel):
    """聊天请求"""
    message: str
    context_limit: int = Field(default=5, ge=1, le=20)
    use_search: bool = True


class ChatResponse(BaseModel):
    """聊天响应"""
    message: ConversationMessagePublic
    assistant_response: ConversationMessagePublic
    context_used: List[str] = []
    suggestions: List[str] = []
    progress_update: Optional[LearningProgress] = None


# 会话状态模型（Redis 缓存）
class SessionState(BaseModel):
    """会话状态"""
    conversation_id: uuid.UUID
    user_id: uuid.UUID
    current_topic: Optional[str] = None
    context_history: List[str] = []
    learning_objectives: List[str] = []
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            uuid.UUID: lambda v: str(v)
        }

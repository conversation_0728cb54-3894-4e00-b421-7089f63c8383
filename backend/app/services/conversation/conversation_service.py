"""
对话服务业务逻辑
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

try:
    import redis
except ImportError:
    redis = None

try:
    import httpx
except ImportError:
    httpx = None

from .models import (
    Conversation, ConversationCreate, ConversationUpdate,
    ConversationMessage, ConversationMessageCreate, ConversationMessagePublic,
    MessageRole, ConversationStatus, LearningProgress,
    SessionState, ChatRequest, ChatResponse
)


class ConversationService:
    """对话管理服务"""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=0)
        self.conversations: Dict[uuid.UUID, Conversation] = {}  # 模拟数据库
        self.messages: Dict[uuid.UUID, List[ConversationMessage]] = {}
    
    def create_conversation(self, user_id: uuid.UUID, conversation_in: ConversationCreate) -> Conversation:
        """创建新对话"""
        conversation = Conversation(
            **conversation_in.model_dump(),
            id=uuid.uuid4(),
            user_id=user_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.conversations[conversation.id] = conversation
        self.messages[conversation.id] = []
        
        # 初始化会话状态
        session_state = SessionState(
            conversation_id=conversation.id,
            user_id=user_id
        )
        self._save_session_state(conversation.id, session_state)
        
        return conversation
    
    def get_conversation(self, conversation_id: uuid.UUID) -> Optional[Conversation]:
        """获取对话"""
        return self.conversations.get(conversation_id)
    
    def update_conversation(self, conversation_id: uuid.UUID, update_data: ConversationUpdate) -> Optional[Conversation]:
        """更新对话"""
        conversation = self.conversations.get(conversation_id)
        if not conversation:
            return None
        
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(conversation, field, value)
        
        conversation.updated_at = datetime.utcnow()
        return conversation
    
    def add_message(self, conversation_id: uuid.UUID, message_in: ConversationMessageCreate) -> Optional[ConversationMessage]:
        """添加消息"""
        if conversation_id not in self.messages:
            return None
        
        message = ConversationMessage(
            **message_in.model_dump(),
            id=uuid.uuid4(),
            conversation_id=conversation_id,
            created_at=datetime.utcnow()
        )
        
        self.messages[conversation_id].append(message)
        return message
    
    def get_messages(self, conversation_id: uuid.UUID, limit: int = 50) -> List[ConversationMessage]:
        """获取对话消息"""
        messages = self.messages.get(conversation_id, [])
        return messages[-limit:] if limit > 0 else messages
    
    def _save_session_state(self, conversation_id: uuid.UUID, state: SessionState):
        """保存会话状态到 Redis"""
        try:
            key = f"session:{conversation_id}"
            self.redis.setex(key, 3600, state.model_dump_json())  # 1小时过期
        except Exception as e:
            print(f"Failed to save session state: {e}")
    
    def _get_session_state(self, conversation_id: uuid.UUID) -> Optional[SessionState]:
        """从 Redis 获取会话状态"""
        try:
            key = f"session:{conversation_id}"
            data = self.redis.get(key)
            if data:
                return SessionState.model_validate_json(data)
        except Exception as e:
            print(f"Failed to get session state: {e}")
        return None


class ContextService:
    """上下文检索服务"""
    
    def __init__(self, manticore_host: str = "localhost", manticore_port: int = 9308):
        self.manticore_url = f"http://{manticore_host}:{manticore_port}"
    
    async def search_context(self, query: str, limit: int = 5) -> List[str]:
        """搜索相关上下文"""
        try:
            async with httpx.AsyncClient() as client:
                # 模拟 Manticore 搜索
                search_data = {
                    "index": "documents",
                    "query": {
                        "match": {"content": query}
                    },
                    "limit": limit
                }
                
                response = await client.post(
                    f"{self.manticore_url}/search",
                    json=search_data,
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    results = response.json()
                    return [hit.get("_source", {}).get("content", "") for hit in results.get("hits", {}).get("hits", [])]
                else:
                    print(f"Search failed: {response.status_code}")
                    return []
                    
        except Exception as e:
            print(f"Context search error: {e}")
            # 返回模拟上下文
            return [
                f"相关内容1：关于 {query} 的基础概念...",
                f"相关内容2：{query} 的实际应用案例...",
                f"相关内容3：{query} 的进阶知识点..."
            ]


class LLMService:
    """LLM 集成服务"""
    
    def __init__(self, api_base: str = "http://localhost:8000"):
        self.api_base = api_base
    
    async def generate_response(
        self, 
        conversation_history: List[ConversationMessage],
        context: List[str],
        user_message: str
    ) -> str:
        """生成 AI 响应"""
        try:
            # 构建提示词
            system_prompt = self._build_system_prompt(context)
            messages = self._build_message_history(conversation_history, user_message)
            
            # 模拟 LLM 调用
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/api/v1/llm/generate",
                    json={
                        "messages": [{"role": "system", "content": system_prompt}] + messages,
                        "max_tokens": 500,
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("content", "抱歉，我现在无法回答这个问题。")
                else:
                    return "抱歉，服务暂时不可用。"
                    
        except Exception as e:
            print(f"LLM generation error: {e}")
            # 返回模拟响应
            return f"这是一个关于 '{user_message}' 的智能回答。基于提供的上下文，我可以为您详细解释相关概念。"
    
    def _build_system_prompt(self, context: List[str]) -> str:
        """构建系统提示词"""
        context_text = "\n".join(f"- {ctx}" for ctx in context)
        return f"""你是一个智能学习导师，专门帮助用户深入理解知识。

相关上下文：
{context_text}

请基于上下文回答用户问题，并：
1. 提供清晰的解释
2. 举出具体例子
3. 引导用户思考
4. 提出相关问题加深理解"""
    
    def _build_message_history(self, history: List[ConversationMessage], current_message: str) -> List[Dict[str, str]]:
        """构建消息历史"""
        messages = []
        
        # 添加历史消息（最近5条）
        for msg in history[-5:]:
            messages.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        # 添加当前用户消息
        messages.append({
            "role": "user",
            "content": current_message
        })
        
        return messages


class ConversationOrchestrator:
    """对话编排服务"""
    
    def __init__(self):
        self.conversation_service = ConversationService()
        self.context_service = ContextService()
        self.llm_service = LLMService()
    
    async def handle_chat(self, conversation_id: uuid.UUID, chat_request: ChatRequest) -> ChatResponse:
        """处理聊天请求"""
        # 1. 获取对话和历史
        conversation = self.conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")
        
        history = self.conversation_service.get_messages(conversation_id, limit=10)
        
        # 2. 搜索相关上下文
        context = []
        if chat_request.use_search:
            context = await self.context_service.search_context(
                chat_request.message, 
                limit=chat_request.context_limit
            )
        
        # 3. 添加用户消息
        user_message = self.conversation_service.add_message(
            conversation_id,
            ConversationMessageCreate(
                role=MessageRole.USER,
                content=chat_request.message,
                context_used=context
            )
        )
        
        # 4. 生成 AI 响应
        ai_response_content = await self.llm_service.generate_response(
            history, context, chat_request.message
        )
        
        # 5. 添加 AI 响应消息
        ai_message = self.conversation_service.add_message(
            conversation_id,
            ConversationMessageCreate(
                role=MessageRole.ASSISTANT,
                content=ai_response_content,
                context_used=context
            )
        )
        
        # 6. 生成学习建议
        suggestions = self._generate_suggestions(chat_request.message, ai_response_content)
        
        # 7. 更新学习进度
        progress = self._update_learning_progress(conversation_id, chat_request.message)
        
        # 转换为 Public 模型
        user_message_public = ConversationMessagePublic(
            id=user_message.id,
            conversation_id=user_message.conversation_id,
            role=user_message.role,
            content=user_message.content,
            context_used=user_message.context_used,
            metadata=user_message.metadata,
            created_at=user_message.created_at
        )

        ai_message_public = ConversationMessagePublic(
            id=ai_message.id,
            conversation_id=ai_message.conversation_id,
            role=ai_message.role,
            content=ai_message.content,
            context_used=ai_message.context_used,
            metadata=ai_message.metadata,
            created_at=ai_message.created_at
        )

        return ChatResponse(
            message=user_message_public,
            assistant_response=ai_message_public,
            context_used=context,
            suggestions=suggestions,
            progress_update=progress
        )
    
    def _generate_suggestions(self, user_message: str, ai_response: str) -> List[str]:
        """生成学习建议"""
        return [
            "尝试提出更具体的问题",
            "可以要求举例说明",
            "询问相关的实际应用",
            "探讨更深层的原理"
        ]
    
    def _update_learning_progress(self, conversation_id: uuid.UUID, message: str) -> LearningProgress:
        """更新学习进度"""
        # 模拟学习进度计算
        return LearningProgress(
            knowledge_points_covered=["基础概念", "实际应用"],
            mastery_levels={"基础概念": 0.7, "实际应用": 0.5},
            questions_asked=1,
            correct_answers=0,
            session_duration=300,
            engagement_score=0.8
        )

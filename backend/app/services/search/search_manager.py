"""
Search manager module.

This module provides a high-level interface for managing search operations
across different search backends and integrating with the document service.
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from app.services.search.manticore_service import ManticoreSearchService
from app.services.embedding.embedding_service import EmbeddingService

logger = logging.getLogger(__name__)


class SearchManager:
    """High-level search manager that coordinates different search services."""
    
    def __init__(
        self,
        manticore_service: Optional[ManticoreSearchService] = None,
        embedding_service: Optional[EmbeddingService] = None
    ):
        """Initialize the search manager.
        
        Args:
            manticore_service: Manticore search service instance
            embedding_service: Embedding service instance
        """
        self.manticore = manticore_service or ManticoreSearchService()
        self.embedding = embedding_service or EmbeddingService()
        self.default_table = "documents"
    
    async def initialize(self) -> bool:
        """Initialize the search manager and create necessary tables.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Create default documents table
            success = await self.manticore.create_documents_table(self.default_table)
            if success:
                logger.info(f"Search manager initialized with table '{self.default_table}'")
            else:
                logger.warning(f"Failed to create table '{self.default_table}' (may already exist)")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize search manager: {e}")
            return False
    
    async def index_document_chunk(
        self,
        doc_id: int,
        title: str,
        content: str,
        source: str = "",
        chunk_index: int = 0,
        table_name: Optional[str] = None
    ) -> bool:
        """Index a document chunk with automatic embedding generation.
        
        Args:
            doc_id: Document ID
            title: Document title
            content: Document content
            source: Document source
            chunk_index: Chunk index
            table_name: Table name (defaults to default_table)
            
        Returns:
            True if indexing successful, False otherwise
        """
        table_name = table_name or self.default_table
        
        try:
            # Generate embedding for the content
            embedding = await self.embedding.get_embedding(content)
            
            # Index the document with embedding
            return await self.manticore.index_document(
                table_name=table_name,
                doc_id=doc_id,
                title=title,
                content=content,
                source=source,
                chunk_index=chunk_index,
                embedding=embedding
            )
        except Exception as e:
            logger.error(f"Failed to index document chunk {doc_id}: {e}")
            return False
    
    async def search(
        self,
        query: str,
        search_type: str = "hybrid",
        limit: int = 10,
        offset: int = 0,
        table_name: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Perform search with different strategies.
        
        Args:
            query: Search query
            search_type: Type of search ("text", "vector", "hybrid")
            limit: Maximum number of results
            offset: Number of results to skip
            table_name: Table name (defaults to default_table)
            filters: Additional filters
            
        Returns:
            Search results dictionary
        """
        table_name = table_name or self.default_table
        
        try:
            if search_type == "text":
                return await self.manticore.search_documents(
                    table_name, query, limit, offset, filters
                )
            elif search_type == "vector":
                # Generate query embedding
                query_embedding = await self.embedding.get_embedding(query)
                return await self.manticore.vector_search(
                    table_name, query_embedding, limit, filters
                )
            elif search_type == "hybrid":
                # Generate query embedding for hybrid search
                query_embedding = await self.embedding.get_embedding(query)
                return await self.manticore.hybrid_search(
                    table_name, query, query_embedding, limit
                )
            else:
                raise ValueError(f"Unsupported search type: {search_type}")
                
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return {
                "hits": [],
                "total": 0,
                "took": 0,
                "timed_out": False,
                "error": str(e)
            }
    
    async def semantic_search(
        self,
        query: str,
        limit: int = 10,
        table_name: Optional[str] = None,
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """Perform semantic search using embeddings.
        
        Args:
            query: Search query
            limit: Maximum number of results
            table_name: Table name (defaults to default_table)
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            Search results dictionary
        """
        table_name = table_name or self.default_table
        
        try:
            # Generate query embedding
            query_embedding = await self.embedding.get_embedding(query)
            
            # Perform vector search
            results = await self.manticore.vector_search(
                table_name, query_embedding, limit
            )
            
            # Filter by similarity threshold if specified
            if similarity_threshold > 0:
                filtered_hits = []
                for hit in results.get("hits", []):
                    score = hit.get("_score", 0)
                    if score >= similarity_threshold:
                        filtered_hits.append(hit)
                
                results["hits"] = filtered_hits
                results["total"] = len(filtered_hits)
            
            return results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return {
                "hits": [],
                "total": 0,
                "took": 0,
                "timed_out": False,
                "error": str(e)
            }
    
    async def delete_document(
        self,
        doc_id: int,
        table_name: Optional[str] = None
    ) -> bool:
        """Delete a document from the search index.
        
        Args:
            doc_id: Document ID to delete
            table_name: Table name (defaults to default_table)
            
        Returns:
            True if deletion successful, False otherwise
        """
        table_name = table_name or self.default_table
        return await self.manticore.delete_document(table_name, doc_id)
    
    async def update_document(
        self,
        doc_id: int,
        updates: Dict[str, Any],
        table_name: Optional[str] = None,
        regenerate_embedding: bool = False
    ) -> bool:
        """Update a document in the search index.
        
        Args:
            doc_id: Document ID to update
            updates: Fields to update
            table_name: Table name (defaults to default_table)
            regenerate_embedding: Whether to regenerate embedding if content changed
            
        Returns:
            True if update successful, False otherwise
        """
        table_name = table_name or self.default_table
        
        try:
            # Regenerate embedding if content was updated
            if regenerate_embedding and "content" in updates:
                embedding = await self.embedding.get_embedding(updates["content"])
                updates["embedding"] = embedding
            
            return await self.manticore.update_document(table_name, doc_id, updates)
            
        except Exception as e:
            logger.error(f"Failed to update document {doc_id}: {e}")
            return False
    
    async def get_search_stats(self, table_name: Optional[str] = None) -> Dict[str, Any]:
        """Get search statistics.
        
        Args:
            table_name: Table name (defaults to default_table)
            
        Returns:
            Search statistics dictionary
        """
        table_name = table_name or self.default_table
        return await self.manticore.get_table_stats(table_name)
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on search services.
        
        Returns:
            Health check results
        """
        results = {
            "manticore": False,
            "embedding": False,
            "overall": False
        }
        
        try:
            # Check Manticore service
            async with self.manticore.client as client:
                results["manticore"] = await client.health_check()
            
            # Check embedding service
            try:
                test_embedding = await self.embedding.get_embedding("test")
                results["embedding"] = len(test_embedding) > 0
            except Exception:
                results["embedding"] = False
            
            results["overall"] = results["manticore"] and results["embedding"]
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            results["error"] = str(e)
        
        return results


# Global search manager instance
_search_manager: Optional[SearchManager] = None


def get_search_manager() -> SearchManager:
    """Get the global search manager instance.
    
    Returns:
        SearchManager instance
    """
    global _search_manager
    if _search_manager is None:
        _search_manager = SearchManager()
    return _search_manager


async def init_search_manager() -> SearchManager:
    """Initialize the global search manager.
    
    Returns:
        Initialized SearchManager instance
    """
    global _search_manager
    _search_manager = SearchManager()
    
    # Initialize the search manager
    await _search_manager.initialize()
    
    logger.info("Search manager initialized successfully")
    return _search_manager

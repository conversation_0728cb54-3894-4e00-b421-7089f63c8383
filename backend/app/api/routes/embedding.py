"""
Embedding API routes

Provides endpoints for text embedding functionality.
"""

import logging
from typing import Any
from fastapi import APIRouter, HTTPException, Depends

from app.api.deps import EmbeddingServiceDep
from app.services.embedding.models import EmbedRequest, EmbedResponse, EmbedItem, BatchEmbedResponse
from app.tasks import enqueue_embedding_batch

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/embed", response_model=EmbedResponse)
async def embed_texts(
    request: EmbedRequest,
    embedding_service: EmbeddingServiceDep
) -> EmbedResponse:
    """
    Embed texts synchronously.
    
    Args:
        request: Embedding request with texts and IDs
        embedding_service: Embedding service dependency
        
    Returns:
        Embedding response with vectors
        
    Raises:
        HTTPException: If embedding fails or input is invalid
    """
    if len(request.ids) != len(request.texts):
        raise HTTPException(
            status_code=400, 
            detail="Number of IDs must match number of texts"
        )
    
    try:
        logger.info(f"Embedding {len(request.texts)} texts")
        
        # Get embeddings from service
        embeddings = await embedding_service.embed_texts(request.texts)
        
        # Validate dimensions
        if embeddings and len(embeddings[0]) != embedding_service.get_embedding_dimension():
            raise HTTPException(
                status_code=500,
                detail=f"Embedding dimension mismatch: got {len(embeddings[0])}, "
                       f"expected {embedding_service.get_embedding_dimension()}"
            )
        
        # Create response
        results = [
            EmbedItem(id=id_, vector=vector)
            for id_, vector in zip(request.ids, embeddings)
        ]
        
        logger.info(f"Successfully embedded {len(results)} texts")
        return EmbedResponse(results=results)
        
    except Exception as e:
        logger.error(f"Embedding failed: {e}")
        raise HTTPException(status_code=500, detail=f"Embedding failed: {str(e)}")


@router.post("/embed/batch", response_model=BatchEmbedResponse)
async def embed_texts_batch(
    request: EmbedRequest
) -> BatchEmbedResponse:
    """
    Embed texts asynchronously using background tasks.
    
    Args:
        request: Embedding request with texts and IDs
        
    Returns:
        Batch response with task information
        
    Raises:
        HTTPException: If input is invalid
    """
    if len(request.ids) != len(request.texts):
        raise HTTPException(
            status_code=400,
            detail="Number of IDs must match number of texts"
        )
    
    try:
        logger.info(f"Queuing batch embedding for {len(request.texts)} texts")
        
        # Enqueue background task
        task = enqueue_embedding_batch(request.ids, request.texts)
        
        return BatchEmbedResponse(
            status="queued",
            task_id=task.message_id,
            batch_size=len(request.texts)
        )
        
    except Exception as e:
        logger.error(f"Failed to queue batch embedding: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to queue batch embedding: {str(e)}"
        )


@router.get("/health")
async def embedding_health_check(
    embedding_service: EmbeddingServiceDep
) -> Any:
    """
    Check embedding service health.
    
    Args:
        embedding_service: Embedding service dependency
        
    Returns:
        Health status information
    """
    try:
        health_status = await embedding_service.health_check()
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

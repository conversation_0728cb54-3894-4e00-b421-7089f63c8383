"""
摘要管理 API 路由
"""
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from app.api.deps import SessionDep, CurrentUser
from app.models.user import User
from app.services.summary.models import (
    SummaryCreate, SummaryUpdate, SummaryPublic,
    SummarizeRequest, SummarizeResponse,
    ConversationSummaryRequest, ConversationSummaryResponse,
    LearningReport, TextAnalysis
)
from app.services.summary.summary_service import (
    SummaryService, TextSummarizationService, ConversationSummaryService
)
from app.services.summary.text_processor import TextProcessor

router = APIRouter()

# 全局服务实例
summary_service = SummaryService()
text_summarization_service = TextSummarizationService()
conversation_summary_service = ConversationSummaryService()
text_processor = TextProcessor()


@router.post("/analyze", response_model=TextAnalysis)
def analyze_text(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    content: str
):
    """分析文本"""
    try:
        analysis = text_processor.analyze_text(content)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate", response_model=SummarizeResponse)
async def generate_summary(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    request: SummarizeRequest
):
    """生成文本摘要"""
    try:
        response = await text_summarization_service.generate_summary(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=SummaryPublic)
def create_summary(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    summary_in: SummaryCreate
):
    """创建摘要记录"""
    try:
        summary = summary_service.create_summary(current_user.id, summary_in)
        
        return SummaryPublic(
            id=summary.id,
            user_id=summary.user_id,
            title=summary.title,
            content=summary.content,
            summary_type=summary.summary_type,
            strategy=summary.strategy,
            source_id=summary.source_id,
            metadata=summary.metadata,
            created_at=summary.created_at,
            updated_at=summary.updated_at
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{summary_id}", response_model=SummaryPublic)
def get_summary(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    summary_id: uuid.UUID
):
    """获取摘要详情"""
    summary = summary_service.get_summary(summary_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    return SummaryPublic(
        id=summary.id,
        user_id=summary.user_id,
        title=summary.title,
        content=summary.content,
        summary_type=summary.summary_type,
        strategy=summary.strategy,
        source_id=summary.source_id,
        metadata=summary.metadata,
        created_at=summary.created_at,
        updated_at=summary.updated_at
    )


@router.post("/conversations/{conversation_id}", response_model=ConversationSummaryResponse)
async def summarize_conversation(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    request: ConversationSummaryRequest
):
    """生成对话摘要"""
    try:
        request.conversation_id = conversation_id
        response = await conversation_summary_service.summarize_conversation(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/learning", response_model=LearningReport)
def get_learning_report(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    period_days: int = 7
):
    """获取学习报告"""
    try:
        # 模拟学习报告生成
        from datetime import datetime
        
        report = LearningReport(
            user_id=current_user.id,
            period_start=datetime.utcnow(),
            period_end=datetime.utcnow(),
            total_sessions=15,
            total_duration=3600,
            documents_studied=8,
            conversations_completed=12,
            knowledge_points_learned=25,
            mastery_improvement={
                "Python基础": 0.3,
                "数据结构": 0.2,
                "算法": 0.1
            },
            strengths=["理论理解能力强", "学习积极性高"],
            areas_for_improvement=["实践应用需加强", "复杂概念理解需深入"],
            recommendations=[
                "增加编程实践练习",
                "尝试完成小项目",
                "复习已学知识点"
            ],
            progress_score=0.75
        )
        
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

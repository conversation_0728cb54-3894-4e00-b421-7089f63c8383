from fastapi import APIRouter, HTTPException, Depends
from app.services.llm import (
    GenerateRequest, 
    GenerateResponse, 
    get_openai_provider,
    fetch_long_term_memory,
    build_prompt,
    OpenAIProvider
)

router = APIRouter()

@router.post("/generate", response_model=GenerateResponse)
async def generate_llm_response(
    request: GenerateRequest,
    llm_provider: OpenAIProvider = Depends(get_openai_provider)
):
    """生成 LLM 响应的主要接口"""
    try:
        # 1. 获取长期记忆上下文
        context = await fetch_long_term_memory(request.conversation_id)
        
        # 2. 构建系统提示
        system_prompt = "You are a helpful AI assistant. Use the provided context to give relevant and accurate responses."
        
        # 3. 构建完整 prompt
        messages = build_prompt(
            system_prompt=system_prompt,
            context=context,
            history=request.history,
            user_query=request.user_query
        )
        
        # 4. 调用 LLM 生成
        result = await llm_provider.generate(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # 5. 构造响应
        return GenerateResponse(
            text=result["text"],
            usage=result["usage"],
            metadata={
                "conversation_id": request.conversation_id,
                "model": result["model"],
                "finish_reason": result["finish_reason"]
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Generation error: {str(e)}")

@router.get("/health")
async def llm_health():
    """LLM 服务健康检查"""
    return {"status": "healthy", "service": "llm-integration"}
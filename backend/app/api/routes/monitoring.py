"""
监控和统计API端点
提供性能监控、错误统计、缓存状态等信息
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any

from app.api.deps import CurrentUser
from app.middleware.performance import get_performance_stats, QueryOptimizationRecommendations
from app.middleware.error_handler import get_error_stats
from app.core.cache import get_cache_stats

router = APIRouter()


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """
    系统健康检查
    """
    return {
        "status": "healthy",
        "timestamp": "2025-08-16T06:00:00Z",
        "version": "1.0.0"
    }


@router.get("/performance", summary="性能统计")
async def get_performance_statistics(
    current_user: CurrentUser,
) -> Dict[str, Any]:
    """
    获取系统性能统计信息
    需要管理员权限
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以查看性能统计"
        )
    
    stats = get_performance_stats()
    
    # 添加优化建议
    recommendations = QueryOptimizationRecommendations.analyze_slow_queries(stats)
    stats["optimization_recommendations"] = recommendations
    
    return {
        "performance_stats": stats,
        "summary": {
            "overall_health": _calculate_health_score(stats),
            "recommendations_count": len(recommendations)
        }
    }


@router.get("/errors", summary="错误统计")
async def get_error_statistics(
    current_user: CurrentUser,
) -> Dict[str, Any]:
    """
    获取系统错误统计信息
    需要管理员权限
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以查看错误统计"
        )
    
    error_stats = get_error_stats()
    
    return {
        "error_stats": error_stats,
        "summary": {
            "error_rate": _calculate_error_rate(error_stats),
            "most_common_error": _get_most_common_error(error_stats),
            "problematic_endpoints": _get_problematic_endpoints(error_stats)
        }
    }


@router.get("/cache", summary="缓存统计")
async def get_cache_statistics(
    current_user: CurrentUser,
) -> Dict[str, Any]:
    """
    获取缓存统计信息
    需要管理员权限
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以查看缓存统计"
        )
    
    cache_stats = get_cache_stats()
    
    return {
        "cache_stats": cache_stats,
        "recommendations": _get_cache_recommendations(cache_stats)
    }


@router.get("/dashboard", summary="监控仪表板")
async def get_monitoring_dashboard(
    current_user: CurrentUser,
) -> Dict[str, Any]:
    """
    获取监控仪表板数据
    需要管理员权限
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以查看监控仪表板"
        )
    
    performance_stats = get_performance_stats()
    error_stats = get_error_stats()
    cache_stats = get_cache_stats()
    
    # 计算总体健康分数
    health_score = _calculate_overall_health_score(
        performance_stats, error_stats, cache_stats
    )
    
    return {
        "health_score": health_score,
        "performance": {
            "api_requests": performance_stats.get("api", {}).get("total_requests", 0),
            "avg_response_time": performance_stats.get("api", {}).get("avg_response_time", 0),
            "slow_request_rate": performance_stats.get("api", {}).get("slow_request_rate", 0),
        },
        "database": {
            "total_queries": performance_stats.get("database", {}).get("total_queries", 0),
            "avg_query_time": performance_stats.get("database", {}).get("avg_query_time", 0),
            "slow_query_rate": performance_stats.get("database", {}).get("slow_query_rate", 0),
        },
        "cache": {
            "hit_rate": cache_stats.get("hit_rate", 0),
            "total_requests": cache_stats.get("total_requests", 0),
            "redis_available": cache_stats.get("redis_available", False),
        },
        "errors": {
            "total_errors": error_stats.get("total_errors", 0),
            "recent_errors_count": len(error_stats.get("recent_errors", [])),
            "most_common_error_type": _get_most_common_error_type(error_stats),
        },
        "alerts": _generate_alerts(performance_stats, error_stats, cache_stats)
    }


@router.post("/cache/clear", summary="清空缓存")
async def clear_cache(
    current_user: CurrentUser,
    pattern: str = "*"
) -> Dict[str, Any]:
    """
    清空缓存
    需要管理员权限
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以清空缓存"
        )
    
    from app.core.cache import cache
    cleared_count = cache.clear(pattern)
    
    return {
        "message": f"成功清空 {cleared_count} 个缓存项",
        "pattern": pattern,
        "cleared_count": cleared_count
    }


def _calculate_health_score(stats: Dict[str, Any]) -> int:
    """计算性能健康分数 (0-100)"""
    score = 100
    
    # API性能评分
    api_stats = stats.get("api", {})
    avg_response_time = api_stats.get("avg_response_time", 0)
    slow_request_rate = api_stats.get("slow_request_rate", 0)
    
    if avg_response_time > 1.0:
        score -= 20
    elif avg_response_time > 0.5:
        score -= 10
    
    if slow_request_rate > 10:
        score -= 20
    elif slow_request_rate > 5:
        score -= 10
    
    # 数据库性能评分
    db_stats = stats.get("database", {})
    slow_query_rate = db_stats.get("slow_query_rate", 0)
    
    if slow_query_rate > 10:
        score -= 20
    elif slow_query_rate > 5:
        score -= 10
    
    # 缓存性能评分
    cache_stats = stats.get("cache", {})
    hit_rate = cache_stats.get("hit_rate", 0)
    
    if hit_rate < 50:
        score -= 15
    elif hit_rate < 70:
        score -= 10
    
    return max(0, score)


def _calculate_error_rate(error_stats: Dict[str, Any]) -> float:
    """计算错误率"""
    total_errors = error_stats.get("total_errors", 0)
    # 这里需要总请求数，暂时使用估算
    estimated_total_requests = total_errors * 10  # 假设错误率为10%
    
    if estimated_total_requests == 0:
        return 0.0
    
    return round(total_errors / estimated_total_requests * 100, 2)


def _get_most_common_error(error_stats: Dict[str, Any]) -> str:
    """获取最常见的错误类型"""
    errors_by_type = error_stats.get("errors_by_type", {})
    if not errors_by_type:
        return "无"
    
    return max(errors_by_type.items(), key=lambda x: x[1])[0]


def _get_most_common_error_type(error_stats: Dict[str, Any]) -> str:
    """获取最常见的错误类型"""
    return _get_most_common_error(error_stats)


def _get_problematic_endpoints(error_stats: Dict[str, Any]) -> list:
    """获取问题端点"""
    errors_by_endpoint = error_stats.get("errors_by_endpoint", {})
    
    # 返回错误数量最多的前3个端点
    sorted_endpoints = sorted(
        errors_by_endpoint.items(),
        key=lambda x: x[1],
        reverse=True
    )
    
    return [{"endpoint": endpoint, "error_count": count} 
            for endpoint, count in sorted_endpoints[:3]]


def _get_cache_recommendations(cache_stats: Dict[str, Any]) -> list:
    """获取缓存优化建议"""
    recommendations = []
    
    hit_rate = cache_stats.get("hit_rate", 0)
    redis_available = cache_stats.get("redis_available", False)
    
    if hit_rate < 50:
        recommendations.append("缓存命中率较低，建议增加缓存时间或缓存更多数据")
    
    if not redis_available:
        recommendations.append("Redis不可用，建议检查Redis连接配置")
    
    if not recommendations:
        recommendations.append("缓存性能良好")
    
    return recommendations


def _calculate_overall_health_score(
    performance_stats: Dict[str, Any],
    error_stats: Dict[str, Any],
    cache_stats: Dict[str, Any]
) -> int:
    """计算总体健康分数"""
    performance_score = _calculate_health_score({"api": performance_stats.get("api", {}), 
                                                "database": performance_stats.get("database", {}),
                                                "cache": cache_stats})
    
    # 错误影响分数
    total_errors = error_stats.get("total_errors", 0)
    error_penalty = min(total_errors * 2, 30)  # 最多扣30分
    
    return max(0, performance_score - error_penalty)


def _generate_alerts(
    performance_stats: Dict[str, Any],
    error_stats: Dict[str, Any],
    cache_stats: Dict[str, Any]
) -> list:
    """生成告警信息"""
    alerts = []
    
    # 性能告警
    api_stats = performance_stats.get("api", {})
    if api_stats.get("slow_request_rate", 0) > 10:
        alerts.append({
            "type": "warning",
            "message": f"慢请求率过高: {api_stats.get('slow_request_rate', 0)}%"
        })
    
    # 数据库告警
    db_stats = performance_stats.get("database", {})
    if db_stats.get("slow_query_rate", 0) > 10:
        alerts.append({
            "type": "warning",
            "message": f"慢查询率过高: {db_stats.get('slow_query_rate', 0)}%"
        })
    
    # 缓存告警
    if cache_stats.get("hit_rate", 0) < 50:
        alerts.append({
            "type": "info",
            "message": f"缓存命中率较低: {cache_stats.get('hit_rate', 0)}%"
        })
    
    # 错误告警
    recent_errors = error_stats.get("recent_errors", [])
    if len(recent_errors) > 10:
        alerts.append({
            "type": "error",
            "message": f"最近错误数量较多: {len(recent_errors)} 个"
        })
    
    return alerts

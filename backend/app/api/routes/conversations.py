"""
对话管理 API 路由
"""
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from app.api.deps import SessionDep, CurrentUser
from app.models.user import User
from app.services.conversation.models import (
    ConversationCreate, ConversationUpdate, ConversationPublic,
    ConversationMessagePublic, ChatRequest, ChatResponse
)
from app.services.conversation.conversation_service import ConversationOrchestrator

router = APIRouter()

# 全局服务实例
orchestrator = ConversationOrchestrator()


@router.post("/", response_model=ConversationPublic)
def create_conversation(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_in: ConversationCreate
):
    """创建新对话"""
    conversation = orchestrator.conversation_service.create_conversation(
        current_user.id, conversation_in
    )
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@router.get("/{conversation_id}", response_model=ConversationPublic)
def get_conversation(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID
):
    """获取对话详情"""
    conversation = orchestrator.conversation_service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@router.post("/{conversation_id}/chat", response_model=ChatResponse)
async def chat(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    chat_request: ChatRequest
):
    """发送聊天消息"""
    try:
        response = await orchestrator.handle_chat(conversation_id, chat_request)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{conversation_id}/messages", response_model=List[ConversationMessagePublic])
def get_conversation_messages(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    limit: int = 50
):
    """获取对话消息"""
    messages = orchestrator.conversation_service.get_messages(conversation_id, limit)
    
    return [
        ConversationMessagePublic(
            id=msg.id,
            conversation_id=msg.conversation_id,
            role=msg.role,
            content=msg.content,
            context_used=msg.context_used,
            metadata=msg.metadata,
            created_at=msg.created_at
        )
        for msg in messages
    ]

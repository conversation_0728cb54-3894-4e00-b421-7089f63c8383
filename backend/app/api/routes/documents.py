import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, BackgroundTasks
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep, DocumentServiceDep, ChunkServiceDep, ProcessingServiceDep
from app.models import (
    Document,
    DocumentCreate,
    DocumentPublic,
    DocumentsPublic,
    DocumentUpdate,
    DocumentChunk,
    DocumentChunkPublic,
    DocumentChunksPublic,
    Message,
)
from app.tasks import enqueue_document_processing, enqueue_document_reprocessing

router = APIRouter(prefix="/documents", tags=["documents"])


@router.get("/", response_model=DocumentsPublic)
def read_documents(
    session: SessionDep, 
    current_user: CurrentUser, 
    skip: int = 0, 
    limit: int = 100
) -> Any:
    """
    Retrieve documents.
    """
    if current_user.is_superuser:
        count_statement = select(func.count()).select_from(Document)
        count = session.exec(count_statement).one()
        statement = select(Document).offset(skip).limit(limit)
        documents = session.exec(statement).all()
    else:
        count_statement = (
            select(func.count())
            .select_from(Document)
            .where(Document.owner_id == current_user.id)
        )
        count = session.exec(count_statement).one()
        statement = (
            select(Document)
            .where(Document.owner_id == current_user.id)
            .offset(skip)
            .limit(limit)
        )
        documents = session.exec(statement).all()

    return DocumentsPublic(data=documents, count=count)


@router.get("/{id}", response_model=DocumentPublic)
def read_document(
    session: SessionDep, 
    current_user: CurrentUser, 
    id: uuid.UUID
) -> Any:
    """
    Get document by ID.
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    return document


@router.post("/", response_model=DocumentPublic)
def create_document(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    document_service: DocumentServiceDep,
    document_in: DocumentCreate,
    auto_process: bool = True
) -> Any:
    """
    Create new document.
    """
    document = document_service.create_document(
        document_in=document_in,
        owner_id=current_user.id,
        auto_process=auto_process
    )
    return document


@router.put("/{id}", response_model=DocumentPublic)
def update_document(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    document_service: DocumentServiceDep,
    id: uuid.UUID,
    document_in: DocumentUpdate,
    reprocess: bool = False
) -> Any:
    """
    Update a document.
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    updated_document = document_service.update_document(
        document_id=id,
        document_in=document_in,
        reprocess=reprocess
    )
    
    if not updated_document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return updated_document


@router.delete("/{id}")
def delete_document(
    session: SessionDep, 
    current_user: CurrentUser, 
    document_service: DocumentServiceDep,
    id: uuid.UUID
) -> Message:
    """
    Delete a document.
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    deleted_document = document_service.delete_document(id)
    if not deleted_document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return Message(message="Document deleted successfully")


@router.get("/{id}/chunks", response_model=DocumentChunksPublic)
def read_document_chunks(
    session: SessionDep,
    current_user: CurrentUser,
    chunk_service: ChunkServiceDep,
    id: uuid.UUID,
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    Get document chunks.
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    chunks = chunk_service.get_document_chunks(
        document_id=id,
        skip=skip,
        limit=limit
    )
    
    return DocumentChunksPublic(data=chunks, count=len(chunks))


@router.post("/{id}/process")
def process_document(
    session: SessionDep,
    current_user: CurrentUser,
    id: uuid.UUID,
    background_tasks: BackgroundTasks
) -> Message:
    """
    Process document (split into chunks).
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    # Enqueue processing task
    background_tasks.add_task(enqueue_document_processing, str(id))
    
    return Message(message="Document processing started")


@router.post("/{id}/reprocess")
def reprocess_document(
    session: SessionDep,
    current_user: CurrentUser,
    id: uuid.UUID,
    background_tasks: BackgroundTasks
) -> Message:
    """
    Reprocess document (regenerate chunks).
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    # Enqueue reprocessing task
    background_tasks.add_task(enqueue_document_reprocessing, str(id))
    
    return Message(message="Document reprocessing started")


@router.get("/{id}/stats")
def get_document_stats(
    session: SessionDep,
    current_user: CurrentUser,
    chunk_service: ChunkServiceDep,
    processing_service: ProcessingServiceDep,
    id: uuid.UUID
) -> Any:
    """
    Get document processing statistics.
    """
    document = session.get(Document, id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    if not current_user.is_superuser and (document.owner_id != current_user.id):
        raise HTTPException(status_code=400, detail="Not enough permissions")
    
    # Get chunk statistics
    chunk_stats = chunk_service.get_chunk_statistics(id)
    
    # Get processing status
    processing_status = processing_service.get_processing_status(str(id))
    
    return {
        "document_id": str(id),
        "document_title": document.title,
        "chunk_statistics": chunk_stats,
        "processing_status": processing_status
    }

"""
Search API routes.

This module provides API endpoints for search functionality using Manticore Search.
"""

import uuid
from typing import Any, Dict, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.api.deps import CurrentUser, DocumentServiceDep
from app.services.search.search_manager import get_search_manager

router = APIRouter(prefix="/search", tags=["search"])


class SearchRequest(BaseModel):
    """Search request model."""
    query: str
    search_type: str = "hybrid"  # "text", "vector", "hybrid"
    limit: int = 10
    offset: int = 0


class SearchResponse(BaseModel):
    """Search response model."""
    hits: list
    total: int
    took: int
    timed_out: bool
    error: Optional[str] = None


class SemanticSearchRequest(BaseModel):
    """Semantic search request model."""
    query: str
    limit: int = 10
    similarity_threshold: float = 0.7


@router.post("/documents", response_model=SearchResponse)
async def search_documents(
    request: SearchRequest,
    current_user: CurrentUser,
    document_service: DocumentServiceDep
) -> Any:
    """
    Search documents using various search strategies.
    
    - **query**: Search query text
    - **search_type**: Type of search ("text", "vector", "hybrid")
    - **limit**: Maximum number of results to return
    - **offset**: Number of results to skip
    """
    try:
        results = await document_service.search_documents(
            query=request.query,
            owner_id=current_user.id if not current_user.is_superuser else None,
            search_type=request.search_type,
            limit=request.limit,
            offset=request.offset
        )
        
        return SearchResponse(**results)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.post("/documents/semantic", response_model=SearchResponse)
async def semantic_search_documents(
    request: SemanticSearchRequest,
    current_user: CurrentUser,
    document_service: DocumentServiceDep
) -> Any:
    """
    Perform semantic search on documents using embeddings.
    
    - **query**: Search query text
    - **limit**: Maximum number of results to return
    - **similarity_threshold**: Minimum similarity threshold (0.0 to 1.0)
    """
    try:
        results = await document_service.semantic_search(
            query=request.query,
            owner_id=current_user.id if not current_user.is_superuser else None,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold
        )
        
        return SearchResponse(**results)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Semantic search failed: {str(e)}")


@router.get("/documents")
async def search_documents_get(
    q: str = Query(..., description="Search query"),
    search_type: str = Query("hybrid", description="Search type: text, vector, or hybrid"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    current_user: CurrentUser = None,
    document_service: DocumentServiceDep = None
) -> Any:
    """
    Search documents using GET request (for simple queries).
    """
    try:
        results = await document_service.search_documents(
            query=q,
            owner_id=current_user.id if not current_user.is_superuser else None,
            search_type=search_type,
            limit=limit,
            offset=offset
        )
        
        return SearchResponse(**results)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/health")
async def search_health_check() -> Dict[str, Any]:
    """
    Check the health of search services.
    """
    try:
        search_manager = get_search_manager()
        health_results = await search_manager.health_check()
        
        return {
            "status": "healthy" if health_results.get("overall") else "unhealthy",
            "details": health_results
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@router.get("/stats")
async def search_stats(current_user: CurrentUser) -> Dict[str, Any]:
    """
    Get search statistics.
    """
    try:
        search_manager = get_search_manager()
        stats = await search_manager.get_search_stats()
        
        return {
            "status": "success",
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get search stats: {str(e)}")


@router.post("/index/document/{document_id}")
async def index_document(
    document_id: uuid.UUID,
    current_user: CurrentUser,
    document_service: DocumentServiceDep
) -> Dict[str, Any]:
    """
    Manually index a specific document for search.
    """
    try:
        # Get the document
        document = document_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check permissions
        if not current_user.is_superuser and document.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not enough permissions")
        
        # Index the document
        success = await document_service.index_document_for_search(document)
        
        return {
            "status": "success" if success else "failed",
            "document_id": str(document_id),
            "indexed": success
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to index document: {str(e)}")


@router.delete("/index/document/{document_id}")
async def remove_document_from_index(
    document_id: uuid.UUID,
    current_user: CurrentUser,
    document_service: DocumentServiceDep
) -> Dict[str, Any]:
    """
    Remove a document from the search index.
    """
    try:
        # Get the document
        document = document_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Check permissions
        if not current_user.is_superuser and document.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not enough permissions")
        
        # Remove from search index
        success = await document_service.remove_from_search_index(document)
        
        return {
            "status": "success" if success else "failed",
            "document_id": str(document_id),
            "removed": success
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to remove document from index: {str(e)}")


@router.post("/initialize")
async def initialize_search(current_user: CurrentUser) -> Dict[str, Any]:
    """
    Initialize search services (admin only).
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    try:
        search_manager = get_search_manager()
        success = await search_manager.initialize()
        
        return {
            "status": "success" if success else "failed",
            "initialized": success
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize search: {str(e)}")

"""
LLM 生成记录相关数据模型
包含 LLM 调用记录、使用统计等相关模型
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlmodel import Field, Relationship, SQLModel


class LLMGenerationStatus(str, Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"


# Shared properties
class LLMGenerationBase(SQLModel):
    model_config = {"protected_namespaces": ()}
    
    conversation_id: str = Field(max_length=255)
    user_query: str
    system_prompt: str | None = None
    context: str | None = None
    response_text: str | None = None
    status: LLMGenerationStatus = Field(default=LLMGenerationStatus.PENDING)
    model_name: str = Field(max_length=100)
    max_tokens: int | None = None
    temperature: float | None = None


# Properties to receive via API on creation
class LLMGenerationCreate(LLMGenerationBase):
    pass


# Properties to receive via API on update
class LLMGenerationUpdate(SQLModel):
    response_text: str | None = None
    status: LLMGenerationStatus | None = None
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    total_tokens: int | None = None
    finish_reason: str | None = None


# Database model, database table inferred from class name
class LLMGeneration(LLMGenerationBase, table=True):
    __tablename__ = "llm_generation"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: datetime | None = None
    
    # Token usage tracking
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    total_tokens: int | None = None
    finish_reason: str | None = None
    
    # Error tracking
    error_message: str | None = None


# Properties to return via API, id is always required
class LLMGenerationPublic(LLMGenerationBase):
    id: uuid.UUID
    created_at: datetime
    completed_at: datetime | None
    prompt_tokens: int | None
    completion_tokens: int | None
    total_tokens: int | None
    finish_reason: str | None
    error_message: str | None


class LLMGenerationsPublic(SQLModel):
    data: list[LLMGenerationPublic]
    count: int
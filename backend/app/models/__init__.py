"""
数据模型模块
重新导出所有模型以保持向后兼容性
"""

# SQLModel 基类（用于 Alembic）
from sqlmodel import SQLModel

# 基础模型
from .base import Message, NewPassword, Token, TokenPayload

# 用户相关模型
from .user import (
    UpdatePassword,
    User,
    UserBase,
    UserCreate,
    UserPublic,
    UserRegister,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
)

# 项目相关模型
from .item import Item, ItemBase, ItemCreate, ItemPublic, ItemsPublic, ItemUpdate

# 文档相关模型
from .document import (
    Document,
    DocumentBase,
    DocumentChunk,
    DocumentChunkBase,
    DocumentChunkCreate,
    DocumentChunkPublic,
    DocumentChunksPublic,
    DocumentChunkUpdate,
    DocumentCreate,
    DocumentPublic,
    DocumentsPublic,
    DocumentUpdate,
)

# 主题相关模型
from .topic import (
    KnowledgePoint,
    KnowledgePointBase,
    KnowledgePointCreate,
    KnowledgePointPublic,
    KnowledgePointsPublic,
    KnowledgePointUpdate,
    Topic,
    TopicBase,
    TopicCreate,
    TopicPublic,
    TopicsPublic,
    TopicUpdate,
)

# 对话相关模型
from .conversation import (
    Conversation,
    ConversationBase,
    ConversationCreate,
    ConversationMessage,
    ConversationMessageBase,
    ConversationMessageCreate,
    ConversationMessagePublic,
    ConversationMessagesPublic,
    ConversationMessageUpdate,
    ConversationPublic,
    ConversationsPublic,
    ConversationStatus,
    ConversationUpdate,
    MessageRole,
)

# LLM 相关模型
from .llm import (
    LLMGeneration,
    LLMGenerationBase,
    LLMGenerationCreate,
    LLMGenerationPublic,
    LLMGenerationsPublic,
    LLMGenerationStatus,
    LLMGenerationUpdate,
)

# 为了保持完全的向后兼容性，确保所有原有的导入都能正常工作
__all__ = [
    # 基础模型
    "Message",
    "Token",
    "TokenPayload",
    "NewPassword",
    # 用户相关模型
    "UserBase",
    "UserCreate",
    "UserRegister",
    "UserUpdate",
    "UserUpdateMe",
    "UpdatePassword",
    "User",
    "UserPublic",
    "UsersPublic",
    # 项目相关模型
    "ItemBase",
    "ItemCreate",
    "ItemUpdate",
    "Item",
    "ItemPublic",
    "ItemsPublic",
    # 文档相关模型
    "DocumentBase",
    "DocumentCreate",
    "DocumentUpdate",
    "Document",
    "DocumentPublic",
    "DocumentsPublic",
    "DocumentChunkBase",
    "DocumentChunkCreate",
    "DocumentChunkUpdate",
    "DocumentChunk",
    "DocumentChunkPublic",
    "DocumentChunksPublic",
    # 主题相关模型
    "TopicBase",
    "TopicCreate",
    "TopicUpdate",
    "Topic",
    "TopicPublic",
    "TopicsPublic",
    "KnowledgePointBase",
    "KnowledgePointCreate",
    "KnowledgePointUpdate",
    "KnowledgePoint",
    "KnowledgePointPublic",
    "KnowledgePointsPublic",
    # 对话相关模型
    "MessageRole",
    "ConversationStatus",
    "ConversationBase",
    "ConversationCreate",
    "ConversationUpdate",
    "Conversation",
    "ConversationPublic",
    "ConversationsPublic",
    "ConversationMessageBase",
    "ConversationMessageCreate",
    "ConversationMessageUpdate",
    "ConversationMessage",
    "ConversationMessagePublic",
    "ConversationMessagesPublic",
    # LLM 相关模型
    "LLMGenerationStatus",
    "LLMGenerationBase",
    "LLMGenerationCreate",
    "LLMGenerationUpdate",
    "LLMGeneration",
    "LLMGenerationPublic",
    "LLMGenerationsPublic",
]

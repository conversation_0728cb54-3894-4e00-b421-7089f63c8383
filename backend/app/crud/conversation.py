"""
对话相关 CRUD 操作
包含对话和消息的创建、更新、查询、删除等操作
"""
import uuid
from datetime import datetime
from typing import Any

from sqlmodel import Session, select

from app.models import (
    Conversation,
    ConversationCreate,
    ConversationUpdate,
    ConversationMessage,
    ConversationMessageCreate,
    ConversationMessageUpdate,
    ConversationStatus,
)
from .base import CRUDBase, get_by_owner, count_by_owner


class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    """对话 CRUD 操作类"""
    
    def create_with_owner(
        self, session: Session, *, obj_in: ConversationCreate, owner_id: uuid.UUID
    ) -> Conversation:
        """创建对话（指定所有者）"""
        obj_in_data = obj_in.model_dump()
        db_obj = Conversation(**obj_in_data, owner_id=owner_id)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def get_multi_by_owner(
        self,
        session: Session,
        *,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Conversation]:
        """根据所有者获取对话列表"""
        return get_by_owner(
            session, model=Conversation, owner_id=owner_id, skip=skip, limit=limit
        )
    
    def get_by_status(
        self,
        session: Session,
        *,
        status: ConversationStatus,
        owner_id: uuid.UUID | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Conversation]:
        """根据状态获取对话列表"""
        statement = select(Conversation).where(Conversation.status == status)
        if owner_id:
            statement = statement.where(Conversation.owner_id == owner_id)
        statement = statement.offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_by_topic(
        self,
        session: Session,
        *,
        topic_id: uuid.UUID,
        owner_id: uuid.UUID | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Conversation]:
        """根据主题获取对话列表"""
        statement = select(Conversation).where(Conversation.topic_id == topic_id)
        if owner_id:
            statement = statement.where(Conversation.owner_id == owner_id)
        statement = statement.offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def count_by_owner(self, session: Session, *, owner_id: uuid.UUID) -> int:
        """统计所有者的对话数量"""
        return count_by_owner(session, model=Conversation, owner_id=owner_id)
    
    def update_status(
        self, session: Session, *, db_obj: Conversation, status: ConversationStatus
    ) -> Conversation:
        """更新对话状态"""
        db_obj.status = status
        db_obj.updated_at = datetime.utcnow()
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj


class CRUDConversationMessage(CRUDBase[ConversationMessage, ConversationMessageCreate, ConversationMessageUpdate]):
    """对话消息 CRUD 操作类"""
    
    def get_by_conversation(
        self,
        session: Session,
        *,
        conversation_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[ConversationMessage]:
        """根据对话 ID 获取消息列表"""
        statement = (
            select(ConversationMessage)
            .where(ConversationMessage.conversation_id == conversation_id)
            .order_by(ConversationMessage.created_at)
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def count_by_conversation(self, session: Session, *, conversation_id: uuid.UUID) -> int:
        """统计对话的消息数量"""
        statement = select(ConversationMessage).where(ConversationMessage.conversation_id == conversation_id)
        return len(session.exec(statement).all())
    
    def get_latest_messages(
        self,
        session: Session,
        *,
        conversation_id: uuid.UUID,
        limit: int = 10
    ) -> list[ConversationMessage]:
        """获取对话的最新消息"""
        statement = (
            select(ConversationMessage)
            .where(ConversationMessage.conversation_id == conversation_id)
            .order_by(ConversationMessage.created_at.desc())
            .limit(limit)
        )
        return session.exec(statement).all()


# 创建 CRUD 实例
conversation = CRUDConversation(Conversation)
conversation_message = CRUDConversationMessage(ConversationMessage)


# 对话相关的兼容性函数
def create_conversation(*, session: Session, conversation_in: ConversationCreate, owner_id: uuid.UUID) -> Conversation:
    """创建对话"""
    return conversation.create_with_owner(session, obj_in=conversation_in, owner_id=owner_id)


def get_conversation(*, session: Session, conversation_id: uuid.UUID) -> Conversation | None:
    """获取对话"""
    return conversation.get(session, id=conversation_id)


def get_conversations_by_owner(
    session: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Conversation]:
    """根据所有者获取对话列表"""
    return conversation.get_multi_by_owner(session, owner_id=owner_id, skip=skip, limit=limit)


def update_conversation(*, session: Session, db_conversation: Conversation, conversation_in: ConversationUpdate) -> Conversation:
    """更新对话"""
    return conversation.update(session, db_obj=db_conversation, obj_in=conversation_in)


def update_conversation_status(*, session: Session, conversation_id: uuid.UUID, status: ConversationStatus) -> Conversation | None:
    """更新对话状态"""
    db_conversation = conversation.get(session, id=conversation_id)
    if db_conversation:
        return conversation.update_status(session, db_obj=db_conversation, status=status)
    return None


# 消息相关的兼容性函数
def create_conversation_message(*, session: Session, message_in: ConversationMessageCreate) -> ConversationMessage:
    """创建对话消息"""
    return conversation_message.create(session, obj_in=message_in)


def get_conversation_messages(
    session: Session, *, conversation_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[ConversationMessage]:
    """获取对话消息列表"""
    return conversation_message.get_by_conversation(session, conversation_id=conversation_id, skip=skip, limit=limit)

"""
项目相关 CRUD 操作
包含项目的创建、更新、查询、删除等操作
"""
import uuid
from typing import Any

from sqlmodel import Session, select

from app.models import Item, ItemCreate, ItemUpdate
from .base import CRUDBase, get_by_owner, count_by_owner


class CRUDItem(CRUDBase[Item, ItemCreate, ItemUpdate]):
    """项目 CRUD 操作类"""
    
    def create_with_owner(
        self, session: Session, *, obj_in: ItemCreate, owner_id: uuid.UUID
    ) -> Item:
        """创建项目（指定所有者）"""
        obj_in_data = obj_in.model_dump()
        db_obj = Item(**obj_in_data, owner_id=owner_id)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def get_multi_by_owner(
        self,
        session: Session,
        *,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Item]:
        """根据所有者获取项目列表"""
        return get_by_owner(
            session, model=Item, owner_id=owner_id, skip=skip, limit=limit
        )
    
    def count_by_owner(self, session: Session, *, owner_id: uuid.UUID) -> int:
        """统计所有者的项目数量"""
        return count_by_owner(session, model=Item, owner_id=owner_id)


# 创建项目 CRUD 实例
item = CRUDItem(Item)


# 为了保持向后兼容性，提供函数式接口
def create_item(*, session: Session, item_in: ItemCreate, owner_id: uuid.UUID) -> Item:
    """创建项目 - 兼容性函数"""
    return item.create_with_owner(session, obj_in=item_in, owner_id=owner_id)


def get_item(*, session: Session, item_id: uuid.UUID) -> Item | None:
    """获取项目 - 兼容性函数"""
    return item.get(session, id=item_id)


def get_items_by_owner(
    session: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Item]:
    """根据所有者获取项目列表 - 兼容性函数"""
    return item.get_multi_by_owner(session, owner_id=owner_id, skip=skip, limit=limit)


def update_item(
    *, session: Session, db_item: Item, item_in: ItemUpdate
) -> Item:
    """更新项目 - 兼容性函数"""
    return item.update(session, db_obj=db_item, obj_in=item_in)


def delete_item(*, session: Session, item_id: uuid.UUID) -> Item | None:
    """删除项目 - 兼容性函数"""
    return item.remove(session, id=item_id)

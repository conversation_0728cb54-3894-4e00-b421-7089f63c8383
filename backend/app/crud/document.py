"""
文档相关 CRUD 操作
包含文档和文档块的创建、更新、查询、删除等操作
"""
import uuid
from datetime import datetime, timezone

from sqlmodel import Session, select
from sqlalchemy.orm import selectinload

from app.models import (
    Document,
    DocumentCreate,
    DocumentUpdate,
    DocumentChunk,
    DocumentChunkCreate,
    DocumentChunkUpdate,
)
from app.core.cache import cached, cache, CacheKeys, invalidate_cache_pattern
from .base import CRUDBase, count_by_owner


class CRUDDocument(CRUDBase[Document, DocumentCreate, DocumentUpdate]):
    """文档 CRUD 操作类"""

    def create_with_owner(
        self, session: Session, *, obj_in: DocumentCreate, owner_id: uuid.UUID
    ) -> Document:
        """创建文档（指定所有者）"""
        obj_in_data = obj_in.model_dump()
        db_obj = Document(**obj_in_data, owner_id=owner_id)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)

        # 缓存新创建的文档
        cache.set(CacheKeys.DOCUMENT_BY_ID.format(document_id=str(db_obj.id)), db_obj, ttl=3600)
        # 使所有者的文档列表缓存失效
        invalidate_cache_pattern(f"documents:owner:{owner_id}:*")

        return db_obj
    
    @cached(ttl=1800, key_prefix="documents_by_owner")
    def get_multi_by_owner(
        self,
        session: Session,
        *,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Document]:
        """根据所有者获取文档列表（带缓存）"""
        # 使用预加载优化查询
        statement = (
            select(Document)
            .where(Document.owner_id == owner_id)
            .options(selectinload(Document.chunks))  # 预加载文档块
            .order_by(Document.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def count_by_owner(self, session: Session, *, owner_id: uuid.UUID) -> int:
        """统计所有者的文档数量"""
        return count_by_owner(session, model=Document, owner_id=owner_id)
    
    def update_timestamp(self, session: Session, *, db_obj: Document) -> Document:
        """更新文档的修改时间"""
        db_obj.updated_at = datetime.now(timezone.utc)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)

        # 更新缓存
        cache.set(CacheKeys.DOCUMENT_BY_ID.format(document_id=str(db_obj.id)), db_obj, ttl=3600)
        # 使所有者的文档列表缓存失效
        invalidate_cache_pattern(f"documents:owner:{db_obj.owner_id}:*")

        return db_obj

    def get_with_cache(self, session: Session, *, id: uuid.UUID) -> Document | None:
        """带缓存的文档获取"""
        # 先尝试从缓存获取
        cached_doc = cache.get(CacheKeys.DOCUMENT_BY_ID.format(document_id=str(id)))
        if cached_doc:
            return cached_doc

        # 从数据库获取并缓存
        statement = select(Document).where(Document.id == id).options(selectinload(Document.chunks))
        doc = session.exec(statement).first()
        if doc:
            cache.set(CacheKeys.DOCUMENT_BY_ID.format(document_id=str(id)), doc, ttl=3600)
        return doc


class CRUDDocumentChunk(CRUDBase[DocumentChunk, DocumentChunkCreate, DocumentChunkUpdate]):
    """文档块 CRUD 操作类"""

    @cached(ttl=3600, key_prefix="document_chunks")
    def get_by_document(
        self,
        session: Session,
        *,
        document_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[DocumentChunk]:
        """根据文档 ID 获取文档块列表（带缓存）"""
        statement = (
            select(DocumentChunk)
            .where(DocumentChunk.document_id == document_id)
            .order_by(DocumentChunk.chunk_index)
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    @cached(ttl=3600, key_prefix="document_chunk_count")
    def count_by_document(self, session: Session, *, document_id: uuid.UUID) -> int:
        """统计文档的块数量（带缓存）"""
        from sqlalchemy import func
        statement = select(func.count(DocumentChunk.id)).where(DocumentChunk.document_id == document_id)
        return session.exec(statement).first() or 0
    
    def delete_by_document(self, session: Session, *, document_id: uuid.UUID) -> int:
        """删除文档的所有块（批量删除优化）"""
        from sqlalchemy import delete, func

        # 先获取要删除的数量
        count_statement = select(func.count(DocumentChunk.id)).where(DocumentChunk.document_id == document_id)
        count = session.exec(count_statement).first() or 0

        # 批量删除
        delete_statement = delete(DocumentChunk).where(DocumentChunk.document_id == document_id)
        session.exec(delete_statement)
        session.commit()

        # 清除相关缓存
        invalidate_cache_pattern(f"document_chunks:*:{document_id}:*")
        invalidate_cache_pattern(f"document_chunk_count:*:{document_id}:*")

        return count


# 创建 CRUD 实例
document = CRUDDocument(Document)
document_chunk = CRUDDocumentChunk(DocumentChunk)


# 文档相关的兼容性函数
def create_document(*, session: Session, document_in: DocumentCreate, owner_id: uuid.UUID) -> Document:
    """创建文档"""
    return document.create_with_owner(session, obj_in=document_in, owner_id=owner_id)


def get_document(*, session: Session, document_id: uuid.UUID) -> Document | None:
    """获取文档"""
    return document.get(session, id=document_id)


def get_documents_by_owner(
    session: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Document]:
    """根据所有者获取文档列表"""
    return document.get_multi_by_owner(session, owner_id=owner_id, skip=skip, limit=limit)


def update_document(*, session: Session, db_document: Document, document_in: DocumentUpdate) -> Document:
    """更新文档"""
    updated_doc = document.update(session, db_obj=db_document, obj_in=document_in)
    return document.update_timestamp(session, db_obj=updated_doc)


def delete_document(*, session: Session, document_id: uuid.UUID) -> Document | None:
    """删除文档"""
    return document.remove(session, id=document_id)


# 文档块相关的兼容性函数
def create_document_chunk(*, session: Session, chunk_in: DocumentChunkCreate) -> DocumentChunk:
    """创建文档块"""
    return document_chunk.create(session, obj_in=chunk_in)


def get_document_chunks(
    session: Session, *, document_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[DocumentChunk]:
    """获取文档块列表"""
    return document_chunk.get_by_document(session, document_id=document_id, skip=skip, limit=limit)


def delete_document_chunks(*, session: Session, document_id: uuid.UUID) -> int:
    """删除文档的所有块"""
    return document_chunk.delete_by_document(session, document_id=document_id)

"""
用户相关 CRUD 操作
包含用户的创建、更新、查询、认证等操作
"""
import uuid
from typing import Any

from sqlmodel import Session, select

from app.core.security import get_password_hash, verify_password
from app.models import User, UserCreate, UserUpdate
from .base import CRUDBase


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """用户 CRUD 操作类"""
    
    def get_by_email(self, session: Session, *, email: str) -> User | None:
        """根据邮箱获取用户"""
        statement = select(User).where(User.email == email)
        return session.exec(statement).first()
    
    def create(self, session: Session, *, obj_in: UserCreate) -> User:
        """创建用户"""
        db_obj = User.model_validate(
            obj_in, update={"hashed_password": get_password_hash(obj_in.password)}
        )
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def update(
        self, session: Session, *, db_obj: User, obj_in: UserUpdate | dict[str, Any]
    ) -> User:
        """更新用户"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        extra_data = {}
        if "password" in update_data:
            password = update_data["password"]
            hashed_password = get_password_hash(password)
            extra_data["hashed_password"] = hashed_password
        
        db_obj.sqlmodel_update(update_data, update=extra_data)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def authenticate(self, session: Session, *, email: str, password: str) -> User | None:
        """用户认证"""
        user = self.get_by_email(session, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    def is_active(self, user: User) -> bool:
        """检查用户是否激活"""
        return user.is_active
    
    def is_superuser(self, user: User) -> bool:
        """检查用户是否为超级用户"""
        return user.is_superuser


# 创建用户 CRUD 实例
user = CRUDUser(User)


# 为了保持向后兼容性，提供函数式接口
def create_user(*, session: Session, user_create: UserCreate) -> User:
    """创建用户 - 兼容性函数"""
    return user.create(session, obj_in=user_create)


def update_user(*, session: Session, db_user: User, user_in: UserUpdate) -> Any:
    """更新用户 - 兼容性函数"""
    return user.update(session, db_obj=db_user, obj_in=user_in)


def get_user_by_email(*, session: Session, email: str) -> User | None:
    """根据邮箱获取用户 - 兼容性函数"""
    return user.get_by_email(session, email=email)


def authenticate(*, session: Session, email: str, password: str) -> User | None:
    """用户认证 - 兼容性函数"""
    return user.authenticate(session, email=email, password=password)

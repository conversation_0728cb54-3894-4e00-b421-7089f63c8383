"""
主题相关 CRUD 操作
包含主题和知识点的创建、更新、查询、删除等操作
"""
import uuid
from datetime import datetime
from typing import Any

from sqlmodel import Session, select

from app.models import (
    Topic,
    TopicCreate,
    TopicUpdate,
    KnowledgePoint,
    KnowledgePointCreate,
    KnowledgePointUpdate,
)
from .base import CRUDBase, get_by_owner, count_by_owner


class CRUDTopic(CRUDBase[Topic, TopicCreate, TopicUpdate]):
    """主题 CRUD 操作类"""
    
    def create_with_owner(
        self, session: Session, *, obj_in: TopicCreate, owner_id: uuid.UUID
    ) -> Topic:
        """创建主题（指定所有者）"""
        obj_in_data = obj_in.model_dump()
        db_obj = Topic(**obj_in_data, owner_id=owner_id)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def get_multi_by_owner(
        self,
        session: Session,
        *,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Topic]:
        """根据所有者获取主题列表"""
        return get_by_owner(
            session, model=Topic, owner_id=owner_id, skip=skip, limit=limit
        )
    
    def get_by_category(
        self,
        session: Session,
        *,
        category: str,
        owner_id: uuid.UUID | None = None,
        skip: int = 0,
        limit: int = 100
    ) -> list[Topic]:
        """根据分类获取主题列表"""
        statement = select(Topic).where(Topic.category == category)
        if owner_id:
            statement = statement.where(Topic.owner_id == owner_id)
        statement = statement.offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def count_by_owner(self, session: Session, *, owner_id: uuid.UUID) -> int:
        """统计所有者的主题数量"""
        return count_by_owner(session, model=Topic, owner_id=owner_id)
    
    def update_timestamp(self, session: Session, *, db_obj: Topic) -> Topic:
        """更新主题的修改时间"""
        db_obj.updated_at = datetime.utcnow()
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj


class CRUDKnowledgePoint(CRUDBase[KnowledgePoint, KnowledgePointCreate, KnowledgePointUpdate]):
    """知识点 CRUD 操作类"""
    
    def get_by_topic(
        self,
        session: Session,
        *,
        topic_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[KnowledgePoint]:
        """根据主题 ID 获取知识点列表"""
        statement = (
            select(KnowledgePoint)
            .where(KnowledgePoint.topic_id == topic_id)
            .order_by(KnowledgePoint.importance.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def get_by_mastery_level(
        self,
        session: Session,
        *,
        topic_id: uuid.UUID,
        mastery_level: int,
        skip: int = 0,
        limit: int = 100
    ) -> list[KnowledgePoint]:
        """根据掌握程度获取知识点列表"""
        statement = (
            select(KnowledgePoint)
            .where(KnowledgePoint.topic_id == topic_id)
            .where(KnowledgePoint.mastery_level == mastery_level)
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def count_by_topic(self, session: Session, *, topic_id: uuid.UUID) -> int:
        """统计主题的知识点数量"""
        statement = select(KnowledgePoint).where(KnowledgePoint.topic_id == topic_id)
        return len(session.exec(statement).all())
    
    def update_mastery_level(
        self, session: Session, *, db_obj: KnowledgePoint, mastery_level: int
    ) -> KnowledgePoint:
        """更新知识点的掌握程度"""
        db_obj.mastery_level = mastery_level
        db_obj.updated_at = datetime.utcnow()
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj


# 创建 CRUD 实例
topic = CRUDTopic(Topic)
knowledge_point = CRUDKnowledgePoint(KnowledgePoint)


# 主题相关的兼容性函数
def create_topic(*, session: Session, topic_in: TopicCreate, owner_id: uuid.UUID) -> Topic:
    """创建主题"""
    return topic.create_with_owner(session, obj_in=topic_in, owner_id=owner_id)


def get_topic(*, session: Session, topic_id: uuid.UUID) -> Topic | None:
    """获取主题"""
    return topic.get(session, id=topic_id)


def get_topics_by_owner(
    session: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Topic]:
    """根据所有者获取主题列表"""
    return topic.get_multi_by_owner(session, owner_id=owner_id, skip=skip, limit=limit)


def update_topic(*, session: Session, db_topic: Topic, topic_in: TopicUpdate) -> Topic:
    """更新主题"""
    updated_topic = topic.update(session, db_obj=db_topic, obj_in=topic_in)
    return topic.update_timestamp(session, db_obj=updated_topic)


def delete_topic(*, session: Session, topic_id: uuid.UUID) -> Topic | None:
    """删除主题"""
    return topic.remove(session, id=topic_id)


# 知识点相关的兼容性函数
def create_knowledge_point(*, session: Session, kp_in: KnowledgePointCreate) -> KnowledgePoint:
    """创建知识点"""
    return knowledge_point.create(session, obj_in=kp_in)


def get_knowledge_points_by_topic(
    session: Session, *, topic_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[KnowledgePoint]:
    """获取主题的知识点列表"""
    return knowledge_point.get_by_topic(session, topic_id=topic_id, skip=skip, limit=limit)


def update_knowledge_point_mastery(
    *, session: Session, kp_id: uuid.UUID, mastery_level: int
) -> KnowledgePoint | None:
    """更新知识点掌握程度"""
    db_kp = knowledge_point.get(session, id=kp_id)
    if db_kp:
        return knowledge_point.update_mastery_level(session, db_obj=db_kp, mastery_level=mastery_level)
    return None

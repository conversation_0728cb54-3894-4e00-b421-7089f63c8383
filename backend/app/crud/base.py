"""
基础 CRUD 操作模式
定义通用的数据库操作模式和工具函数
"""
import uuid
from typing import Any, Generic, Type, TypeVar

from pydantic import BaseModel
from sqlmodel import Session, SQLModel, select

# 定义泛型类型
ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础 CRUD 操作类"""
    
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        
        **Parameters**
        * `model`: A SQLModel model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    def get(self, session: Session, id: Any) -> ModelType | None:
        """根据 ID 获取单个记录"""
        return session.get(self.model, id)

    def get_multi(
        self, session: Session, *, skip: int = 0, limit: int = 100
    ) -> list[ModelType]:
        """获取多个记录"""
        statement = select(self.model).offset(skip).limit(limit)
        return session.exec(statement).all()

    def create(self, session: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建新记录"""
        obj_in_data = obj_in.model_dump()
        db_obj = self.model(**obj_in_data)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj

    def update(
        self,
        session: Session,
        *,
        db_obj: ModelType,
        obj_in: UpdateSchemaType | dict[str, Any]
    ) -> ModelType:
        """更新记录"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        db_obj.sqlmodel_update(update_data)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj

    def remove(self, session: Session, *, id: Any) -> ModelType | None:
        """删除记录"""
        obj = session.get(self.model, id)
        if obj:
            session.delete(obj)
            session.commit()
        return obj


def get_by_owner(
    session: Session,
    *,
    model: Type[ModelType],
    owner_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100
) -> list[ModelType]:
    """根据所有者 ID 获取记录列表"""
    statement = (
        select(model)
        .where(model.owner_id == owner_id)  # type: ignore
        .offset(skip)
        .limit(limit)
    )
    return session.exec(statement).all()


def count_by_owner(
    session: Session,
    *,
    model: Type[ModelType],
    owner_id: uuid.UUID
) -> int:
    """统计所有者的记录数量"""
    statement = select(model).where(model.owner_id == owner_id)  # type: ignore
    return len(session.exec(statement).all())

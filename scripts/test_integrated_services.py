#!/usr/bin/env python3
"""
集成服务测试脚本
验证 Conversation 和 Summary 服务的集成效果
"""
import sys
import os
import subprocess
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试服务导入"""
    print("📦 测试服务导入...")
    
    try:
        # 测试 Conversation 服务导入
        from backend.app.services.conversation.models import (
            ConversationCreate, ConversationPublic, ChatRequest, ChatResponse
        )
        from backend.app.services.conversation.conversation_service import (
            ConversationService, ConversationOrchestrator
        )
        print("✅ Conversation 服务导入成功")
        
        # 测试 Summary 服务导入
        from backend.app.services.summary.models import (
            SummaryCreate, SummaryPublic, SummarizeRequest, SummarizeResponse
        )
        from backend.app.services.summary.summary_service import (
            SummaryService, TextSummarizationService
        )
        from backend.app.services.summary.text_processor import TextProcessor
        print("✅ Summary 服务导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_functionality():
    """测试服务功能"""
    print("\n🔧 测试服务功能...")
    
    try:
        import uuid
        from datetime import datetime
        
        # 测试 Conversation 服务
        from backend.app.services.conversation.conversation_service import ConversationService
        from backend.app.services.conversation.models import ConversationCreate, LearningLevel
        
        conv_service = ConversationService()
        user_id = uuid.uuid4()
        
        conversation_in = ConversationCreate(
            title="集成测试对话",
            learning_level=LearningLevel.BEGINNER
        )
        
        conversation = conv_service.create_conversation(user_id, conversation_in)
        assert conversation.title == "集成测试对话"
        print("✅ Conversation 服务功能正常")
        
        # 测试 Summary 服务
        from backend.app.services.summary.summary_service import SummaryService
        from backend.app.services.summary.models import SummaryCreate, SummaryType, SummaryStrategy
        
        summary_service = SummaryService()
        
        summary_in = SummaryCreate(
            title="集成测试摘要",
            content="这是一个集成测试的摘要内容",
            summary_type=SummaryType.DOCUMENT,
            strategy=SummaryStrategy.HYBRID,
            source_id=uuid.uuid4()
        )
        
        summary = summary_service.create_summary(user_id, summary_in)
        assert summary.title == "集成测试摘要"
        print("✅ Summary 服务功能正常")
        
        # 测试文本处理器
        from backend.app.services.summary.text_processor import TextProcessor
        
        processor = TextProcessor()
        analysis = processor.analyze_text("这是一个测试文本，用于验证文本处理功能。")
        assert analysis.word_count > 0
        print("✅ 文本处理器功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_routes():
    """测试 API 路由"""
    print("\n🌐 测试 API 路由...")
    
    try:
        # 检查路由文件是否存在
        routes_dir = project_root / "backend" / "app" / "api" / "routes"
        
        conversations_route = routes_dir / "conversations.py"
        summaries_route = routes_dir / "summaries.py"
        
        if conversations_route.exists():
            print("✅ Conversations API 路由文件存在")
        else:
            print("❌ Conversations API 路由文件不存在")
            return False
        
        if summaries_route.exists():
            print("✅ Summaries API 路由文件存在")
        else:
            print("❌ Summaries API 路由文件不存在")
            return False
        
        # 检查主路由注册
        main_api = project_root / "backend" / "app" / "api" / "main.py"
        if main_api.exists():
            content = main_api.read_text()
            
            if "conversations" in content and "summaries" in content:
                print("✅ 主路由注册包含新服务")
            else:
                print("⚠️ 主路由注册可能不完整")
        
        return True
        
    except Exception as e:
        print(f"❌ API 路由测试失败: {e}")
        return False


def test_backend_startup():
    """测试后端启动"""
    print("\n🚀 测试后端启动...")
    
    try:
        # 切换到 backend 目录
        backend_dir = project_root / "backend"
        os.chdir(backend_dir)
        
        # 尝试导入主应用
        sys.path.insert(0, str(backend_dir))
        
        from app.main import app
        print("✅ 后端应用导入成功")
        
        # 检查路由是否正确注册
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        conversation_routes = [r for r in routes if 'conversation' in r]
        summary_routes = [r for r in routes if 'summar' in r]
        
        if conversation_routes:
            print(f"✅ 发现 Conversation 路由: {len(conversation_routes)} 个")
        else:
            print("⚠️ 未发现 Conversation 路由")
        
        if summary_routes:
            print(f"✅ 发现 Summary 路由: {len(summary_routes)} 个")
        else:
            print("⚠️ 未发现 Summary 路由")
        
        return True
        
    except Exception as e:
        print(f"❌ 后端启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_integration_test():
    """运行集成测试"""
    print("\n🧪 运行集成测试...")
    
    try:
        # 运行现有的集成测试
        backend_dir = project_root / "backend"
        test_script = backend_dir / "test_existing_imports.py"
        
        if test_script.exists():
            result = subprocess.run([
                sys.executable, str(test_script)
            ], cwd=backend_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 现有导入测试通过")
            else:
                print(f"⚠️ 现有导入测试有警告: {result.stdout}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 集成服务测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("功能测试", test_service_functionality),
        ("路由测试", test_api_routes),
        ("启动测试", test_backend_startup),
        ("集成测试", run_integration_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！集成成功！")
        print("\n📋 集成完成情况:")
        print("✅ Conversation Service - 对话管理服务")
        print("✅ Summary Service - 摘要生成服务")
        print("✅ API 路由集成")
        print("✅ Backend 服务集成")
        
        print("\n🚀 下一步建议:")
        print("1. 启动后端服务: cd backend && uv run uvicorn app.main:app --reload")
        print("2. 访问 API 文档: http://localhost:8000/docs")
        print("3. 测试新的 API 端点")
        print("4. 集成前端界面")
        
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

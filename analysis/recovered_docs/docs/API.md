# API 文档

Master-Know 系统提供完整的 RESTful API，支持文档管理、搜索、用户认证等功能。

## 基础信息

- **Base URL**: `http://api.localhost.tiangolo.com`
- **API 版本**: v1
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`

## 在线文档

访问 [Swagger UI](http://api.localhost.tiangolo.com/docs) 查看完整的交互式 API 文档。

## 认证

### 获取访问令牌

```http
POST /api/v1/login/access-token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=changethis
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### 使用令牌

在后续请求中添加 Authorization 头：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 核心 API 端点

### 1. 系统健康检查

```http
GET /api/v1/utils/health-check/
```

**响应**: `true` (系统正常)

### 2. 用户管理

#### 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer {token}
```

#### 创建新用户
```http
POST /api/v1/users/
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "newpassword",
  "full_name": "User Name"
}
```

### 3. 文档管理

#### 创建文档
```http
POST /api/v1/documents/
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "文档标题",
  "content": "文档内容...",
  "description": "文档描述",
  "file_type": "text/markdown",
  "size": 1024
}
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "文档标题",
  "description": "文档描述",
  "created_at": "2025-08-15T10:30:00Z",
  "updated_at": "2025-08-15T10:30:00Z",
  "owner_id": "user-id"
}
```

#### 获取文档列表
```http
GET /api/v1/documents/?skip=0&limit=20
Authorization: Bearer {token}
```

#### 获取单个文档
```http
GET /api/v1/documents/{document_id}
Authorization: Bearer {token}
```

#### 更新文档
```http
PUT /api/v1/documents/{document_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": "更新后的内容..."
}
```

#### 删除文档
```http
DELETE /api/v1/documents/{document_id}
Authorization: Bearer {token}
```

### 4. 文档处理

#### 触发文档处理
```http
POST /api/v1/documents/{document_id}/process
Authorization: Bearer {token}
```

**说明**: 异步处理文档，包括文本分割、向量化等步骤。

#### 获取文档块
```http
GET /api/v1/documents/{document_id}/chunks
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "count": 5,
  "data": [
    {
      "id": "chunk-id-1",
      "content": "文档块内容...",
      "chunk_index": 0,
      "start_char": 0,
      "end_char": 500
    }
  ]
}
```

### 5. 搜索功能

#### 文档搜索
```http
POST /api/v1/search/documents
Authorization: Bearer {token}
Content-Type: application/json

{
  "query": "搜索关键词",
  "limit": 10,
  "filters": {
    "document_ids": ["doc-id-1", "doc-id-2"]
  }
}
```

**响应示例**:
```json
{
  "hits": [
    {
      "document_id": "doc-id-1",
      "chunk_id": "chunk-id-1",
      "content": "匹配的文档内容...",
      "score": 0.95,
      "highlights": ["关键词"]
    }
  ],
  "total": 1,
  "query_time": 0.05
}
```

## 错误处理

API 使用标准 HTTP 状态码：

- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证错误
- `500` - 服务器内部错误

**错误响应格式**:
```json
{
  "detail": "错误描述信息"
}
```

**验证错误响应**:
```json
{
  "detail": [
    {
      "loc": ["body", "title"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 速率限制

- 每个用户每分钟最多 100 个请求
- 搜索 API 每分钟最多 20 个请求
- 文档处理 API 每分钟最多 10 个请求

## 示例代码

### Python 示例

```python
import httpx
import asyncio

async def main():
    base_url = "http://api.localhost.tiangolo.com"
    
    async with httpx.AsyncClient() as client:
        # 登录获取令牌
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        response = await client.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        token = response.json()["access_token"]
        
        # 使用令牌访问 API
        headers = {"Authorization": f"Bearer {token}"}
        
        # 创建文档
        doc_data = {
            "title": "测试文档",
            "content": "这是一个测试文档的内容",
            "description": "测试用途",
            "file_type": "text/plain",
            "size": 100
        }
        response = await client.post(
            f"{base_url}/api/v1/documents/",
            json=doc_data,
            headers=headers
        )
        document = response.json()
        print(f"创建文档: {document['id']}")
        
        # 搜索文档
        search_data = {"query": "测试", "limit": 5}
        response = await client.post(
            f"{base_url}/api/v1/search/documents",
            json=search_data,
            headers=headers
        )
        results = response.json()
        print(f"搜索结果: {len(results['hits'])} 个")

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript 示例

```javascript
const baseURL = 'http://api.localhost.tiangolo.com';

// 登录获取令牌
async function login() {
    const response = await fetch(`${baseURL}/api/v1/login/access-token`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'username=<EMAIL>&password=changethis'
    });
    const data = await response.json();
    return data.access_token;
}

// 创建文档
async function createDocument(token) {
    const response = await fetch(`${baseURL}/api/v1/documents/`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: '测试文档',
            content: '这是一个测试文档的内容',
            description: '测试用途',
            file_type: 'text/plain',
            size: 100
        })
    });
    return await response.json();
}

// 使用示例
async function main() {
    const token = await login();
    const document = await createDocument(token);
    console.log('创建文档:', document.id);
}
```

## 测试工具

项目提供了完整的测试脚本：

```bash
# 运行完整的 API 测试
python3 test_system_integration.py

# 运行简化的文档处理测试
python3 test_document_simple.py
```

## 相关文档

- [部署指南](./DEPLOYMENT.md) - 了解如何部署系统
- [故障排除](./TROUBLESHOOTING.md) - 解决常见问题
- [开发指南](./DEVELOPMENT.md) - 进行二次开发
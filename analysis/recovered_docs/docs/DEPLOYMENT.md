# 部署指南

本文档详细说明了 Master-Know 系统的部署流程和配置要求。

## 系统要求

### 硬件要求
- **内存**: 最少 4GB，推荐 8GB
- **存储**: 最少 10GB 可用空间
- **CPU**: 2 核心以上

### 软件要求
- **Docker Desktop** (macOS/Windows) 或 **Docker Engine** (Linux)
- **Docker Compose** v2.0+
- **Python 3.10+** (用于运行测试脚本)

### 网络端口
确保以下端口未被占用：
- `5173` - 前端开发服务器
- `9306` - Manticore Search MySQL 协议
- `9308` - Manticore Search HTTP API
- `6379` - Redis
- `5432` - PostgreSQL
- `8080` - Adminer 数据库管理界面

## 环境配置

### 1. 环境变量设置

复制环境变量模板：
```bash
cp .env.example .env
```

**关键配置项**：

```bash
# 域名配置 (重要: 必须设置为 localhost.tiangolo.com)
DOMAIN=localhost.tiangolo.com
FRONTEND_HOST=http://localhost:5173

# 数据库配置
POSTGRES_SERVER=db
POSTGRES_PORT=5432
POSTGRES_DB=master_know
POSTGRES_USER=master_know_user
POSTGRES_PASSWORD=changethis

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379

# 嵌入服务配置
EMBEDDING_OPENAI_API_KEY=your_openai_api_key_here
EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1
EMBEDDING_DEFAULT_MODEL=text-embedding-3-small
EMBEDDING_DIM=1536

# 安全配置
SECRET_KEY=your_secret_key_here
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changethis
```

### 2. 生成安全密钥

```bash
# 生成新的 SECRET_KEY
python3 -c "import secrets; print(secrets.token_urlsafe(32))"
```

## 部署流程

### 方式一：开发模式 (推荐)

使用 `docker compose watch` 启动，支持代码热重载：

```bash
# 启动所有服务
docker compose watch

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f
```

### 方式二：生产模式

```bash
# 构建并启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 停止服务
docker compose down
```

### 3. 验证部署

运行系统集成测试：

```bash
# 安装测试依赖
pip3 install httpx redis psycopg2-binary semantic-text-splitter

# 运行完整测试
python3 test_system_integration.py

# 运行简化测试
python3 test_document_simple.py
```

预期输出：
```
✅ 基础服务集成测试: 5/5 通过
✅ 端到端功能测试: 3/3 通过
```

## 服务访问

部署成功后，可以通过以下地址访问各个服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:5173 | React 前端界面 |
| Backend API | http://api.localhost.tiangolo.com | FastAPI 后端服务 |
| API 文档 | http://api.localhost.tiangolo.com/docs | Swagger UI |
| 数据库管理 | http://localhost:8080 | Adminer 界面 |
| Traefik 仪表板 | http://localhost:8090 | 负载均衡器状态 |

### 默认登录信息

- **管理员账号**: <EMAIL>
- **默认密码**: changethis
- **数据库**: 
  - 用户名: master_know_user
  - 密码: changethis
  - 数据库: master_know

## 数据持久化

系统使用 Docker volumes 进行数据持久化：

```bash
# 查看数据卷
docker volume ls | grep master-know

# 备份数据库
docker exec master-know-db-1 pg_dump -U master_know_user master_know > backup.sql

# 恢复数据库
docker exec -i master-know-db-1 psql -U master_know_user master_know < backup.sql
```

## 服务管理

### 重启特定服务

```bash
# 重启 backend 服务
docker compose restart backend

# 重启 dramatiq worker
docker compose restart dramatiq-worker

# 重新构建并重启服务
docker compose up -d --build backend
```

### 查看日志

```bash
# 查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f backend
docker compose logs -f dramatiq-worker

# 查看最近的日志
docker compose logs --tail 50 backend
```

### 进入容器调试

```bash
# 进入 backend 容器
docker exec -it master-know-backend-1 bash

# 进入数据库容器
docker exec -it master-know-db-1 psql -U master_know_user master_know

# 进入 Redis 容器
docker exec -it master-know-redis-1 redis-cli
```

## 性能优化

### 资源限制

在生产环境中，建议为容器设置资源限制：

```yaml
# docker-compose.override.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
  
  db:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### 监控

```bash
# 查看资源使用情况
docker stats

# 查看特定容器资源使用
docker stats master-know-backend-1
```

## 更新部署

### 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker compose up -d --build

# 运行测试验证
python3 test_system_integration.py
```

### 数据库迁移

```bash
# 运行数据库迁移
docker exec master-know-backend-1 alembic upgrade head
```

## 下一步

- 查看 [API 文档](./API.md) 了解接口详情
- 查看 [故障排除文档](./TROUBLESHOOTING.md) 解决常见问题
- 查看 [开发文档](./DEVELOPMENT.md) 进行二次开发
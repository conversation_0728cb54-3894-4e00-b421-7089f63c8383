# 系统架构文档

Master-Know 是一个基于微服务架构的个人化 AI 学习系统，采用现代化的技术栈和容器化部署。

## 整体架构

```mermaid
graph TB
    User[用户] --> Frontend[React 前端]
    Frontend --> Traefik[Traefik 代理]
    Traefik --> Backend[FastAPI 后端]
    Backend --> DB[(PostgreSQL)]
    Backend --> Redis[(Redis)]
    Backend --> Manticore[(Manticore Search)]
    Backend --> Queue[Dramatiq 队列]
    Queue --> Worker[Dramatiq Worker]
    Worker --> Embedding[嵌入服务]
    Worker --> TextSplitter[文本分割引擎]
```

## 核心组件

### 1. 前端层 (Frontend)

**技术栈**: React 18 + TypeScript + Chakra UI

**职责**:
- 用户界面渲染
- 用户交互处理
- API 请求管理
- 状态管理

**关键特性**:
- 响应式设计
- 实时更新
- 组件化架构
- TypeScript 类型安全

### 2. 网关层 (Traefik)

**技术栈**: Traefik v3

**职责**:
- 反向代理
- 负载均衡
- SSL 终止
- 路由管理

**路由配置**:
```yaml
# 前端路由
- Host: localhost.tiangolo.com
  Service: frontend

# API 路由  
- Host: api.localhost.tiangolo.com
  Service: backend
```

### 3. 应用层 (Backend)

**技术栈**: FastAPI + SQLModel + Pydantic

**架构模式**: 分层架构 + 依赖注入

```
app/
├── api/           # API 路由层
├── core/          # 核心配置
├── models/        # 数据模型
├── services/      # 业务逻辑层
└── utils/         # 工具函数
```

**核心服务**:
- **用户服务**: 认证、授权、用户管理
- **文档服务**: 文档 CRUD、元数据管理
- **搜索服务**: 全文搜索、向量搜索
- **处理服务**: 异步任务调度

### 4. 数据层

#### PostgreSQL (主数据库)

**用途**: 
- 用户数据存储
- 文档元数据
- 系统配置
- 关系数据管理

**关键表结构**:
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    hashed_password VARCHAR NOT NULL,
    full_name VARCHAR,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    title VARCHAR NOT NULL,
    content TEXT,
    file_type VARCHAR,
    size INTEGER,
    owner_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 文档块表
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    chunk_index INTEGER,
    start_char INTEGER,
    end_char INTEGER,
    embedding VECTOR(1536)
);
```

#### Redis (缓存层)

**用途**:
- 会话存储
- 任务队列
- 缓存热点数据
- 实时数据

**数据结构**:
```
# 用户会话
session:{user_id} -> {session_data}

# 任务队列
dramatiq:default -> [task1, task2, ...]

# 缓存
cache:document:{id} -> {document_data}
```

#### Manticore Search (搜索引擎)

**用途**:
- 全文搜索
- 向量搜索
- 混合搜索
- 实时索引

**索引配置**:
```sql
-- 文档索引
CREATE TABLE documents_index (
    id BIGINT,
    title TEXT,
    content TEXT,
    embedding FLOAT_VECTOR
) ENGINE='columnar';
```

### 5. 处理层 (Workers)

#### Dramatiq Worker

**技术栈**: Dramatiq + Redis

**任务类型**:
- 文档处理任务
- 向量化任务
- 索引更新任务
- 清理任务

**任务流程**:
```python
@dramatiq.actor
async def process_document(document_id: str):
    # 1. 获取文档内容
    document = await get_document(document_id)
    
    # 2. 文本分割
    chunks = text_splitter.split(document.content)
    
    # 3. 向量化
    embeddings = await embedding_service.embed_texts(chunks)
    
    # 4. 存储到数据库
    await save_chunks(document_id, chunks, embeddings)
    
    # 5. 更新搜索索引
    await update_search_index(document_id, chunks)
```

### 6. 引擎层

#### 文本分割引擎

**技术栈**: Rust + Python 绑定

**分割策略**:
- Token 基础分割
- 语义分割
- 递归分割
- 自适应分割

```python
class TextSplitterEngine:
    def split_text(self, text: str, strategy: str) -> List[str]:
        if strategy == "token_based":
            return self._split_by_tokens(text)
        elif strategy == "semantic":
            return self._split_by_semantics(text)
```

#### 嵌入服务

**技术栈**: OpenAI API + AsyncOpenAI

**模型支持**:
- text-embedding-3-small (1536 维)
- text-embedding-3-large (3072 维)
- 自定义模型支持

```python
class EmbeddingService:
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        response = await self.client.embeddings.create(
            model=self.model_name,
            input=texts
        )
        return [item.embedding for item in response.data]
```

## 数据流

### 1. 文档处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant Q as 队列
    participant W as Worker
    participant E as 嵌入服务
    participant D as 数据库
    participant S as 搜索引擎

    U->>F: 上传文档
    F->>B: POST /documents/
    B->>D: 保存文档元数据
    B->>Q: 提交处理任务
    B->>F: 返回文档ID
    F->>U: 显示上传成功

    Q->>W: 分发处理任务
    W->>D: 获取文档内容
    W->>W: 文本分割
    W->>E: 生成向量
    E->>W: 返回向量
    W->>D: 保存文档块
    W->>S: 更新搜索索引
```

### 2. 搜索流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant E as 嵌入服务
    participant S as 搜索引擎
    participant D as 数据库

    U->>F: 输入搜索查询
    F->>B: POST /search/documents
    B->>E: 生成查询向量
    E->>B: 返回查询向量
    B->>S: 执行混合搜索
    S->>B: 返回搜索结果
    B->>D: 获取文档详情
    D->>B: 返回文档数据
    B->>F: 返回搜索结果
    F->>U: 显示搜索结果
```

## 网络架构

### 容器网络

```yaml
networks:
  default:
    driver: bridge
  traefik-public:
    external: true
```

### 服务通信

```mermaid
graph LR
    subgraph "Docker Network"
        Frontend[frontend:5173]
        Backend[backend:8000]
        DB[db:5432]
        Redis[redis:6379]
        Manticore[manticore:9306/9308]
        Worker[dramatiq-worker]
    end
    
    subgraph "External"
        User[用户]
        OpenAI[OpenAI API]
    end
    
    User --> Frontend
    Frontend --> Backend
    Backend --> DB
    Backend --> Redis
    Backend --> Manticore
    Worker --> DB
    Worker --> Redis
    Worker --> Manticore
    Worker --> OpenAI
```

### 端口映射

| 服务 | 内部端口 | 外部端口 | 协议 |
|------|----------|----------|------|
| Frontend | 5173 | 5173 | HTTP |
| Backend | 8000 | - | HTTP (通过 Traefik) |
| PostgreSQL | 5432 | 5432 | TCP |
| Redis | 6379 | 6379 | TCP |
| Manticore | 9306/9308 | 9306/9308 | MySQL/HTTP |
| Adminer | 8080 | 8080 | HTTP |
| Traefik | 80/443 | 80/443 | HTTP/HTTPS |

## 安全架构

### 认证授权

```mermaid
graph TD
    User[用户] --> Login[登录接口]
    Login --> JWT[JWT Token]
    JWT --> Auth[认证中间件]
    Auth --> API[API 接口]
    Auth --> RBAC[角色权限控制]
```

**JWT 配置**:
```python
# Token 配置
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 8  # 8 天
ALGORITHM = "HS256"
SECRET_KEY = "your-secret-key"

# 权限装饰器
@require_permission("document:read")
async def get_document(document_id: str, user: User = Depends(get_current_user)):
    pass
```

### 数据安全

- **密码加密**: bcrypt 哈希
- **API 密钥**: 环境变量存储
- **数据库连接**: SSL 加密
- **容器隔离**: Docker 网络隔离

## 监控与日志

### 日志架构

```python
# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default"
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["console"]
    }
}
```

### 健康检查

```python
@router.get("/health-check/")
async def health_check():
    """系统健康检查"""
    checks = {
        "database": await check_database(),
        "redis": await check_redis(),
        "manticore": await check_manticore()
    }
    return {"status": "healthy", "checks": checks}
```

## 扩展性设计

### 水平扩展

- **无状态设计**: 所有服务都是无状态的
- **负载均衡**: Traefik 支持多实例
- **数据库分片**: 支持读写分离
- **缓存分布**: Redis 集群支持

### 插件架构

```python
# 插件接口
class ProcessorPlugin:
    def process(self, document: Document) -> ProcessResult:
        raise NotImplementedError

# 插件注册
PROCESSORS = {
    "pdf": PDFProcessor(),
    "markdown": MarkdownProcessor(),
    "text": TextProcessor()
}
```

## 性能优化

### 数据库优化

```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_documents_owner_created 
ON documents(owner_id, created_at DESC);

-- 分区表
CREATE TABLE document_chunks_2024 PARTITION OF document_chunks
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### 缓存策略

```python
# 多级缓存
@cache.memoize(timeout=300)  # Redis 缓存
async def get_document(doc_id: str):
    return await db.get_document(doc_id)

# 应用级缓存
@lru_cache(maxsize=1000)
def get_user_permissions(user_id: str):
    return db.get_user_permissions(user_id)
```

### 异步处理

```python
# 异步 I/O
async def process_multiple_documents(doc_ids: List[str]):
    tasks = [process_document(doc_id) for doc_id in doc_ids]
    results = await asyncio.gather(*tasks)
    return results
```

## 相关文档

- [部署指南](./DEPLOYMENT.md) - 系统部署说明
- [API 文档](./API.md) - API 接口详情
- [开发指南](./DEVELOPMENT.md) - 开发环境配置
- [故障排除](./TROUBLESHOOTING.md) - 问题解决方案
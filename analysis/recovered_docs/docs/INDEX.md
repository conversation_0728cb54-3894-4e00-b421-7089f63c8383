# Master-Know 文档中心

欢迎来到 Master-Know 项目的文档中心！这里汇集了项目的所有重要文档，帮助您深入了解项目的方方面面。

## 📋 快速导航

### 🚀 新用户必读
- **[部署指南](./DEPLOYMENT.md)** - 一键部署完整系统 ⭐
- **[API 文档](./API.md)** - 完整的 API 接口说明 ⭐
- **[故障排除](./TROUBLESHOOTING.md)** - 常见问题解决方案 ⭐

### 🛠️ 开发者文档
- **[开发指南](./DEVELOPMENT.md)** - 本地开发环境搭建
- **[系统架构](./ARCHITECTURE.md)** - 详细的架构设计文档
- **[集成测试指南](./3_Engineering/04_integration_testing_guide.md)** - 测试策略和自动化测试

### 🎯 产品与设计
- **[项目简介](./1_Product/01_brief.md)** - 项目的核心理念、目标用户和价值主张
- **[产品需求文档 (PRD)](./1_Product/02_prd_mvp.md)** - MVP 版本的详细功能需求和用户故事
- **[数据模型](./2_Architecture/02_data_model_v1.md)** - 数据库设计和实体关系

## 🎯 快速开始

### 第一次使用？按这个顺序：

1. **[部署指南](./DEPLOYMENT.md)** - 5分钟部署完整系统
2. **[API 文档](./API.md)** - 了解如何使用 API
3. **[开发指南](./DEVELOPMENT.md)** - 开始开发和定制

### 遇到问题？

1. **[故障排除](./TROUBLESHOOTING.md)** - 查看常见问题解决方案
2. **[系统架构](./ARCHITECTURE.md)** - 深入了解系统设计

## 📊 系统状态

当前系统状态：✅ **完全正常运行**

- ✅ 网络连接问题已修复
- ✅ 文档处理流程正常
- ✅ 所有集成测试通过
- ✅ API 端点正常工作

**最新修复**：
- 修复了 macOS Docker Desktop 环境下的域名配置问题
- 更新了 API 端点为 `http://api.localhost.tiangolo.com`
- 修复了嵌入服务配置字段名不匹配问题

## 📚 完整文档索引

### 🏗️ 架构设计
- **[系统概览](./2_Architecture/01_overview.md)** - 整体架构和技术选型
- **[模块化重构计划](./2_Architecture/03_modular_refactor_plan.md)** - 代码组织和模块划分
- **[实现指南](./2_Architecture/04_implementation_guide.md)** - 具体的实现细节和最佳实践
- **[Manticore 集成](./2_Architecture/05_manticore_integration.md)** - 搜索引擎集成方案

### ⚙️ 工程指南
- **[配置管理](./3_Engineering/03_config_management.md)** - 环境变量和配置文件管理

### 📋 项目管理
- **[项目蓝图](./PROJECT_BLUEPRINT.md)** - 项目的整体规划和里程碑
- **[主题服务完成报告](./TOPIC_SERVICE_COMPLETION_REPORT.md)** - 核心功能的实现总结

## 🧪 测试与验证

系统提供了完整的测试脚本来验证部署和功能：

```bash
# 运行完整的系统集成测试
python3 test_system_integration.py

# 运行简化的文档处理测试
python3 test_document_simple.py
```

## 🤝 贡献指南

我们欢迎任何形式的贡献！如果您发现文档有误或需要补充，请：

1. 提交 Issue 描述问题
2. 或者直接提交 Pull Request 修复

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **首先查看** [故障排除文档](./TROUBLESHOOTING.md)
2. **运行诊断脚本** `python3 test_system_integration.py`
3. **查看日志** `docker compose logs -f`
4. **提交 Issue** 并附上诊断信息

---

**最后更新**: 2025年8月15日

✨ **系统已完全修复并正常运行！** 感谢您对 Master-Know 项目的关注和支持！
#!/usr/bin/env python3
"""
摘要服务 POC 集成到主系统脚本
"""
import os
import shutil
from pathlib import Path


def integrate_summary_service():
    """集成摘要服务到主系统"""
    print("🔄 开始集成 Summary Service...")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent
    
    # 源目录和目标目录
    poc_dir = Path(__file__).parent
    target_service_dir = project_root / "services" / "summary"
    target_backend_dir = project_root / "backend" / "app" / "services" / "summary"
    
    try:
        # 1. 集成到独立服务目录
        print("📁 集成到独立服务目录...")
        
        # 复制服务文件到 services/summary
        files_to_copy = [
            ("simple_models.py", "models.py"),
            ("services.py", "services.py"),
            ("processors.py", "processors.py"),
            ("main.py", "main.py"),
            ("requirements.txt", "requirements.txt")
        ]
        
        target_service_dir.mkdir(parents=True, exist_ok=True)
        
        for src_file, dst_file in files_to_copy:
            src_path = poc_dir / src_file
            dst_path = target_service_dir / dst_file
            
            if src_path.exists():
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制 {src_file} -> {dst_path}")
            else:
                print(f"⚠️ 源文件不存在: {src_file}")
        
        # 2. 集成到 backend 服务目录
        print("📁 集成到 backend 服务目录...")
        
        target_backend_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建 __init__.py
        init_file = target_backend_dir / "__init__.py"
        init_file.write_text('"""摘要服务模块"""\n')
        
        # 复制核心服务文件
        backend_files = [
            ("services.py", "summary_service.py"),
            ("processors.py", "text_processor.py"),
            ("simple_models.py", "models.py")
        ]
        
        for src_file, dst_file in backend_files:
            src_path = poc_dir / src_file
            dst_path = target_backend_dir / dst_file
            
            if src_path.exists():
                # 读取源文件内容并适配 backend 环境
                content = src_path.read_text()
                
                # 修改导入路径
                content = content.replace(
                    "from simple_models import",
                    "from .models import"
                )
                content = content.replace(
                    "from processors import",
                    "from .text_processor import"
                )
                
                dst_path.write_text(content)
                print(f"✅ 适配并复制 {src_file} -> {dst_path}")
        
        # 3. 创建 API 路由
        print("📁 创建 API 路由...")
        
        api_routes_dir = project_root / "backend" / "app" / "api" / "routes"
        summaries_route = api_routes_dir / "summaries.py"
        
        if not summaries_route.exists():
            route_content = '''"""
摘要管理 API 路由
"""
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from app.api.deps import get_db, get_current_active_user
from app.models.user import User
from app.services.summary.models import (
    SummaryCreate, SummaryUpdate, SummaryPublic,
    SummarizeRequest, SummarizeResponse,
    ConversationSummaryRequest, ConversationSummaryResponse,
    LearningReport, TextAnalysis
)
from app.services.summary.summary_service import (
    SummaryService, TextSummarizationService, ConversationSummaryService
)
from app.services.summary.text_processor import TextProcessor

router = APIRouter()

# 全局服务实例
summary_service = SummaryService()
text_summarization_service = TextSummarizationService()
conversation_summary_service = ConversationSummaryService()
text_processor = TextProcessor()


@router.post("/analyze", response_model=TextAnalysis)
def analyze_text(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    content: str
):
    """分析文本"""
    try:
        analysis = text_processor.analyze_text(content)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate", response_model=SummarizeResponse)
async def generate_summary(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    request: SummarizeRequest
):
    """生成文本摘要"""
    try:
        response = await text_summarization_service.generate_summary(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=SummaryPublic)
def create_summary(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    summary_in: SummaryCreate
):
    """创建摘要记录"""
    try:
        summary = summary_service.create_summary(current_user.id, summary_in)
        
        return SummaryPublic(
            id=summary.id,
            user_id=summary.user_id,
            title=summary.title,
            content=summary.content,
            summary_type=summary.summary_type,
            strategy=summary.strategy,
            source_id=summary.source_id,
            metadata=summary.metadata,
            created_at=summary.created_at,
            updated_at=summary.updated_at
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{summary_id}", response_model=SummaryPublic)
def get_summary(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    summary_id: uuid.UUID
):
    """获取摘要详情"""
    summary = summary_service.get_summary(summary_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    return SummaryPublic(
        id=summary.id,
        user_id=summary.user_id,
        title=summary.title,
        content=summary.content,
        summary_type=summary.summary_type,
        strategy=summary.strategy,
        source_id=summary.source_id,
        metadata=summary.metadata,
        created_at=summary.created_at,
        updated_at=summary.updated_at
    )


@router.post("/conversations/{conversation_id}", response_model=ConversationSummaryResponse)
async def summarize_conversation(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    conversation_id: uuid.UUID,
    request: ConversationSummaryRequest
):
    """生成对话摘要"""
    try:
        request.conversation_id = conversation_id
        response = await conversation_summary_service.summarize_conversation(request)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/learning", response_model=LearningReport)
def get_learning_report(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    period_days: int = 7
):
    """获取学习报告"""
    try:
        # 模拟学习报告生成
        from datetime import datetime
        
        report = LearningReport(
            user_id=current_user.id,
            period_start=datetime.utcnow(),
            period_end=datetime.utcnow(),
            total_sessions=15,
            total_duration=3600,
            documents_studied=8,
            conversations_completed=12,
            knowledge_points_learned=25,
            mastery_improvement={
                "Python基础": 0.3,
                "数据结构": 0.2,
                "算法": 0.1
            },
            strengths=["理论理解能力强", "学习积极性高"],
            areas_for_improvement=["实践应用需加强", "复杂概念理解需深入"],
            recommendations=[
                "增加编程实践练习",
                "尝试完成小项目",
                "复习已学知识点"
            ],
            progress_score=0.75
        )
        
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
'''
            summaries_route.write_text(route_content)
            print(f"✅ 创建 API 路由: {summaries_route}")
        
        # 4. 更新主路由注册
        print("📁 更新主路由注册...")
        
        main_api = project_root / "backend" / "app" / "api" / "main.py"
        if main_api.exists():
            content = main_api.read_text()
            
            # 检查是否已经包含摘要路由
            if "summaries" not in content:
                # 添加导入
                if "from .routes import" in content:
                    content = content.replace(
                        "from .routes import",
                        "from .routes import summaries,"
                    )
                
                # 添加路由注册
                if 'api_router.include_router(' in content:
                    # 找到最后一个路由注册位置
                    lines = content.split('\n')
                    insert_index = -1
                    for i, line in enumerate(lines):
                        if 'api_router.include_router(' in line:
                            insert_index = i + 1
                    
                    if insert_index > 0:
                        new_route = 'api_router.include_router(summaries.router, prefix="/summaries", tags=["summaries"])'
                        lines.insert(insert_index, new_route)
                        content = '\n'.join(lines)
                
                main_api.write_text(content)
                print(f"✅ 更新主路由注册: {main_api}")
        
        print("\n🎉 Summary Service 集成完成！")
        print("\n📋 集成结果:")
        print(f"✅ 独立服务: {target_service_dir}")
        print(f"✅ Backend 服务: {target_backend_dir}")
        print(f"✅ API 路由: {summaries_route}")
        
        print("\n🚀 下一步:")
        print("1. 重启 backend 服务")
        print("2. 访问 /docs 查看新的 API 端点")
        print("3. 测试摘要功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 Summary Service POC 集成工具")
    print("=" * 50)
    
    success = integrate_summary_service()
    
    if success:
        print("\n✅ 集成成功完成！")
    else:
        print("\n❌ 集成失败，请检查错误信息")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

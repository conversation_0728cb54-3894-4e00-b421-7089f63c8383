# Summary Service POC

## 概述

摘要生成服务的概念验证，实现智能学习摘要和知识点提取功能。

## 核心功能

1. **文档摘要生成**
   - 自动提取文档关键信息
   - 生成结构化摘要
   - 支持多种摘要策略（抽取式、生成式）

2. **对话摘要**
   - 总结学习对话的关键内容
   - 提取讨论的知识点
   - 生成学习回顾报告

3. **知识点提取**
   - 识别文档中的核心概念
   - 构建知识点关系图
   - 评估知识点重要性

4. **学习报告生成**
   - 生成个性化学习报告
   - 分析学习进度和效果
   - 提供改进建议

## 技术栈

- **FastAPI**: Web 框架
- **SQLModel**: 数据模型和 ORM
- **LLM Integration**: 智能摘要生成
- **Text Processing**: 文本分析和处理
- **Manticore Search**: 内容检索和分析

## 文件结构

```
summary_poc/
├── README.md              # 本文档
├── requirements.txt       # 依赖包
├── main.py               # 主服务入口
├── models.py             # 数据模型
├── services.py           # 业务逻辑
├── processors.py         # 文本处理器
├── test_poc.py           # POC 测试
├── run_poc.py            # 运行脚本
└── integrate_to_main.py  # 集成脚本
```

## 运行方式

```bash
# 安装依赖
pip install -r requirements.txt

# 运行 POC 测试
python test_poc.py

# 启动服务
python run_poc.py

# 集成到主系统
python integrate_to_main.py
```

## 验证目标

- [x] 文档摘要生成
- [x] 对话内容总结
- [x] 知识点自动提取
- [x] 学习报告生成
- [x] 与主系统集成兼容性

#!/usr/bin/env python3
"""
Embedding POC Test Script
测试 embedding 服务的基本功能
"""

import asyncio
import aiohttp
import json
import os
from typing import List, Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_TEXTS = [
    "这是一个测试文档的第一段内容。",
    "人工智能技术正在快速发展。",
    "FastAPI 是一个现代的 Python Web 框架。",
    "向量数据库可以用于语义搜索。",
    "Dramatiq 是一个简单的任务队列库。"
]

async def test_health_check():
    """测试健康检查接口"""
    print("🔍 Testing health check...")
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/health") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Health check passed: {data}")
                return True
            else:
                print(f"❌ Health check failed: {response.status}")
                return False

async def test_embedding_sync():
    """测试同步 embedding 接口"""
    print("\n🔍 Testing synchronous embedding...")
    
    test_data = {
        "ids": [f"chunk_{i}" for i in range(len(TEST_TEXTS))],
        "texts": TEST_TEXTS
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/embed",
            json=test_data,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                data = await response.json()
                results = data.get("results", [])
                print(f"✅ Sync embedding successful: {len(results)} vectors generated")
                
                # Validate results
                if results:
                    first_vector = results[0]["vector"]
                    print(f"   Vector dimension: {len(first_vector)}")
                    print(f"   First few values: {first_vector[:5]}")
                
                return True
            else:
                error_text = await response.text()
                print(f"❌ Sync embedding failed: {response.status} - {error_text}")
                return False

async def test_embedding_batch():
    """测试异步批量 embedding 接口"""
    print("\n🔍 Testing asynchronous batch embedding...")
    
    test_data = {
        "ids": [f"batch_chunk_{i}" for i in range(len(TEST_TEXTS))],
        "texts": TEST_TEXTS
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/embed/batch",
            json=test_data,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Batch embedding queued: {data}")
                return True
            else:
                error_text = await response.text()
                print(f"❌ Batch embedding failed: {response.status} - {error_text}")
                return False

async def test_dimension_consistency():
    """测试向量维度一致性"""
    print("\n🔍 Testing vector dimension consistency...")
    
    # Test with different batch sizes
    batch_sizes = [1, 3, 5]
    dimensions = []
    
    for batch_size in batch_sizes:
        test_data = {
            "ids": [f"dim_test_{i}" for i in range(batch_size)],
            "texts": TEST_TEXTS[:batch_size]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{BASE_URL}/embed",
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    results = data.get("results", [])
                    if results:
                        dim = len(results[0]["vector"])
                        dimensions.append(dim)
                        print(f"   Batch size {batch_size}: dimension {dim}")
    
    # Check consistency
    if len(set(dimensions)) == 1:
        print(f"✅ Dimension consistency test passed: all vectors have {dimensions[0]} dimensions")
        return True
    else:
        print(f"❌ Dimension consistency test failed: found dimensions {set(dimensions)}")
        return False

async def test_error_handling():
    """测试错误处理"""
    print("\n🔍 Testing error handling...")
    
    # Test mismatched ids and texts
    test_data = {
        "ids": ["id1", "id2"],
        "texts": ["text1"]  # Mismatch
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/embed",
            json=test_data,
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status == 400:
                print("✅ Error handling test passed: correctly rejected mismatched input")
                return True
            else:
                print(f"❌ Error handling test failed: expected 400, got {response.status}")
                return False

async def run_all_tests():
    """运行所有测试"""
    print("🚀 Starting Embedding POC Tests")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Synchronous Embedding", test_embedding_sync),
        ("Batch Embedding", test_embedding_batch),
        ("Dimension Consistency", test_dimension_consistency),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Embedding POC is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the service configuration.")

if __name__ == "__main__":
    # Check if server is running
    print("📋 Embedding POC Test Suite")
    print(f"🌐 Testing server at: {BASE_URL}")
    print(f"📝 Test texts: {len(TEST_TEXTS)} samples")
    print()
    
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")

version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: embedding-poc-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  embedding-service:
    build: .
    container_name: embedding-poc-service
    ports:
      - "8000:8000"
    environment:
      - EMBEDDING_REMOTE_URL=${EMBEDDING_REMOTE_URL}
      - REMOTE_API_KEY=${REMOTE_API_KEY}
      - EMBEDDING_MODEL_NAME=${EMBEDDING_MODEL_NAME:-text-embedding-3-small}
      - EMBEDDING_DIM=${EMBEDDING_DIM:-1536}
      - EMBEDDING_BATCH_SIZE=${EMBEDDING_BATCH_SIZE:-64}
      - REDIS_URL=redis://redis:6379
      - PORT=8000
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  dramatiq-worker:
    build: .
    container_name: embedding-poc-worker
    command: dramatiq demo.embedding_poc.main
    environment:
      - EMBEDDING_REMOTE_URL=${EMBEDDING_REMOTE_URL}
      - REMOTE_API_KEY=${REMOTE_API_KEY}
      - EMBEDDING_MODEL_NAME=${EMBEDDING_MODEL_NAME:-text-embedding-3-small}
      - EMBEDDING_DIM=${EMBEDDING_DIM:-1536}
      - EMBEDDING_BATCH_SIZE=${EMBEDDING_BATCH_SIZE:-64}
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  redis_data:
    driver: local

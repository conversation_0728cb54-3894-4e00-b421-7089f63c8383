#!/usr/bin/env python3
"""
Test script for Gateway POC
Tests all major functionality including auth, aggregation, rate limiting, and WebSocket
"""

import asyncio
import aiohttp
import websockets
import json
import time
from typing import Dict, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost"
WS_URL = "ws://localhost"


class GatewayTester:
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self) -> bool:
        """Test basic health check"""
        logger.info("Testing health check...")
        try:
            async with self.session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Health check passed: {data}")
                    return True
                else:
                    logger.error(f"Health check failed with status {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Health check error: {str(e)}")
            return False
    
    async def test_authentication(self) -> bool:
        """Test JWT authentication"""
        logger.info("Testing authentication...")
        
        # Test login
        try:
            login_data = {"username": "demo", "password": "demo123"}
            async with self.session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data["access_token"]
                    logger.info("Login successful")
                else:
                    logger.error(f"Login failed with status {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
        
        # Test protected endpoint
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            async with self.session.get(f"{BASE_URL}/api/v1/auth/me", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Protected endpoint access successful: {data}")
                    return True
                else:
                    logger.error(f"Protected endpoint failed with status {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Protected endpoint error: {str(e)}")
            return False
    
    async def test_service_aggregation(self) -> bool:
        """Test service aggregation functionality"""
        logger.info("Testing service aggregation...")
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"} if self.auth_token else {}
            topic_id = "test-topic-123"
            
            async with self.session.get(f"{BASE_URL}/api/v1/aggregate/topic/{topic_id}", headers=headers) as response:
                if response.status in [200, 503]:  # 503 expected due to mock services
                    data = await response.json()
                    logger.info(f"Service aggregation response: {json.dumps(data, indent=2)}")
                    
                    # Check if response has expected structure
                    if "topic_id" in data and "services" in data:
                        logger.info("Service aggregation structure is correct")
                        return True
                    else:
                        logger.error("Service aggregation response structure is incorrect")
                        return False
                else:
                    logger.error(f"Service aggregation failed with status {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Service aggregation error: {str(e)}")
            return False
    
    async def test_llm_aggregation(self) -> bool:
        """Test LLM aggregation with context"""
        logger.info("Testing LLM aggregation...")
        
        if not self.auth_token:
            logger.error("Authentication required for LLM aggregation test")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            payload = {
                "topic_id": "test-topic-123",
                "prompt": "What is machine learning?",
                "model": "gpt-3.5-turbo"
            }
            
            async with self.session.post(f"{BASE_URL}/api/v1/aggregate/llm/generate", 
                                       json=payload, headers=headers) as response:
                if response.status in [200, 503]:  # 503 expected due to mock services
                    data = await response.json()
                    logger.info(f"LLM aggregation response: {json.dumps(data, indent=2)}")
                    return True
                else:
                    logger.error(f"LLM aggregation failed with status {response.status}")
                    return False
        except Exception as e:
            logger.error(f"LLM aggregation error: {str(e)}")
            return False
    
    async def test_rate_limiting(self) -> bool:
        """Test rate limiting functionality"""
        logger.info("Testing rate limiting...")
        
        try:
            # Make multiple rapid requests to trigger rate limiting
            tasks = []
            for i in range(10):
                task = self.session.get(f"{BASE_URL}/")
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            rate_limited = False
            for response in responses:
                if isinstance(response, Exception):
                    continue
                if response.status == 429:
                    rate_limited = True
                    logger.info("Rate limiting is working")
                    break
            
            if not rate_limited:
                logger.info("Rate limiting not triggered (may be due to high limits)")
            
            return True
            
        except Exception as e:
            logger.error(f"Rate limiting test error: {str(e)}")
            return False
    
    async def test_websocket(self) -> bool:
        """Test WebSocket proxy functionality"""
        logger.info("Testing WebSocket proxy...")
        
        try:
            topic_id = "test-topic-websocket"
            uri = f"{WS_URL}/api/v1/ws/conversation/{topic_id}"
            
            async with websockets.connect(uri) as websocket:
                # Wait for connection message
                connection_msg = await websocket.recv()
                connection_data = json.loads(connection_msg)
                logger.info(f"WebSocket connection established: {connection_data}")
                
                # Send a test message
                test_message = {
                    "type": "user_message",
                    "content": "Hello, this is a test message",
                    "timestamp": time.time()
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                response_msg = await websocket.recv()
                response_data = json.loads(response_msg)
                logger.info(f"WebSocket response: {response_data}")
                
                if response_data.get("type") == "message_response":
                    logger.info("WebSocket proxy is working correctly")
                    return True
                else:
                    logger.error("WebSocket response format is incorrect")
                    return False
                    
        except Exception as e:
            logger.error(f"WebSocket test error: {str(e)}")
            return False


async def run_all_tests():
    """Run all gateway tests"""
    logger.info("Starting Gateway POC tests...")
    
    async with GatewayTester() as tester:
        tests = [
            ("Health Check", tester.test_health_check),
            ("Authentication", tester.test_authentication),
            ("Service Aggregation", tester.test_service_aggregation),
            ("LLM Aggregation", tester.test_llm_aggregation),
            ("Rate Limiting", tester.test_rate_limiting),
            ("WebSocket Proxy", tester.test_websocket),
        ]
        
        results = {}
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                results[test_name] = result
                status = "PASSED" if result else "FAILED"
                logger.info(f"Test {test_name}: {status}")
            except Exception as e:
                logger.error(f"Test {test_name} crashed: {str(e)}")
                results[test_name] = False
        
        # Print summary
        logger.info(f"\n{'='*50}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Gateway POC is working correctly.")
        else:
            logger.warning(f"⚠️  {total - passed} tests failed. Check the logs above.")


if __name__ == "__main__":
    asyncio.run(run_all_tests())

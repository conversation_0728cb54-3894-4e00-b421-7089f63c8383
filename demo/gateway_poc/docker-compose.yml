services:
  traefik:
    image: traefik:v3.3
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.letsencrypt.acme.email=admin@localhost"
      - "--certificatesresolvers.letsencrypt.acme.storage=/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./acme.json:/acme.json
    networks:
      - traefik-public

  bff:
    build: .
    environment:
      - BFF_HOST=0.0.0.0
      - BFF_PORT=8000
      - AUTH_JWT_SECRET=dev-secret-key-change-in-production
      - REDIS_URL=redis://redis:6379/0
      - USER_SERVICE_URL=http://mock-user:8001
      - DOCUMENT_SERVICE_URL=http://mock-document:8002
      - TOPIC_SERVICE_URL=http://mock-topic:8003
      - LLM_SERVICE_URL=http://mock-llm:8004
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.bff.rule=Host(`localhost`)"
      - "traefik.http.routers.bff.entrypoints=web"
      - "traefik.http.services.bff.loadbalancer.server.port=8000"
      # HTTPS configuration
      - "traefik.http.routers.bff-secure.rule=Host(`localhost`)"
      - "traefik.http.routers.bff-secure.entrypoints=websecure"
      - "traefik.http.routers.bff-secure.tls.certresolver=letsencrypt"
    networks:
      - traefik-public
      - internal
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    networks:
      - internal

  # Mock backend services for testing
  mock-user:
    image: kennethreitz/httpbin
    networks:
      - internal

  mock-document:
    image: kennethreitz/httpbin
    networks:
      - internal

  mock-topic:
    image: kennethreitz/httpbin
    networks:
      - internal

  mock-llm:
    image: kennethreitz/httpbin
    networks:
      - internal

networks:
  traefik-public:
    external: false
  internal:
    external: false

volumes:
  redis_data:

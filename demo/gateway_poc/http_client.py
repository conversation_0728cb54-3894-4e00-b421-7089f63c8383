import httpx
import asyncio
from typing import Dict, Any, List, Optional
from fastapi import HTTPEx<PERSON>, status
from config import settings
import logging

logger = logging.getLogger(__name__)


class ServiceClient:
    """HTTP client for backend services"""
    
    def __init__(self):
        self.timeout = httpx.Timeout(30.0, connect=5.0)
        self.limits = httpx.Limits(max_keepalive_connections=20, max_connections=100)
    
    async def get(self, url: str, headers: Optional[Dict[str, str]] = None, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request to backend service"""
        async with httpx.AsyncClient(timeout=self.timeout, limits=self.limits) as client:
            try:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error {e.response.status_code} for GET {url}: {e.response.text}")
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"Backend service error: {e.response.text}"
                )
            except httpx.RequestError as e:
                logger.error(f"Request error for GET {url}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Backend service unavailable: {str(e)}"
                )
    
    async def post(self, url: str, json_data: Optional[Dict[str, Any]] = None, 
                   headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make POST request to backend service"""
        async with httpx.AsyncClient(timeout=self.timeout, limits=self.limits) as client:
            try:
                response = await client.post(url, json=json_data, headers=headers)
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error {e.response.status_code} for POST {url}: {e.response.text}")
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"Backend service error: {e.response.text}"
                )
            except httpx.RequestError as e:
                logger.error(f"Request error for POST {url}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Backend service unavailable: {str(e)}"
                )
    
    async def aggregate_requests(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Make multiple concurrent requests and return aggregated results"""
        async with httpx.AsyncClient(timeout=self.timeout, limits=self.limits) as client:
            tasks = []
            
            for req in requests:
                method = req.get("method", "GET").upper()
                url = req["url"]
                headers = req.get("headers")
                
                if method == "GET":
                    task = client.get(url, headers=headers, params=req.get("params"))
                elif method == "POST":
                    task = client.post(url, json=req.get("json"), headers=headers)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                tasks.append(task)
            
            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                results = []
                
                for i, response in enumerate(responses):
                    if isinstance(response, Exception):
                        logger.error(f"Request {i} failed: {str(response)}")
                        results.append({
                            "error": True,
                            "message": str(response),
                            "request": requests[i]
                        })
                    else:
                        try:
                            response.raise_for_status()
                            results.append({
                                "error": False,
                                "data": response.json(),
                                "status_code": response.status_code
                            })
                        except Exception as e:
                            logger.error(f"Response processing failed for request {i}: {str(e)}")
                            results.append({
                                "error": True,
                                "message": str(e),
                                "status_code": getattr(response, 'status_code', None),
                                "request": requests[i]
                            })
                
                return results
                
            except Exception as e:
                logger.error(f"Aggregate request failed: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Service aggregation failed: {str(e)}"
                )


# Global service client instance
service_client = ServiceClient()

from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # BFF Configuration
    bff_host: str = "0.0.0.0"
    bff_port: int = 8000
    bff_debug: bool = False
    
    # JWT Authentication
    auth_jwt_secret: str = "your-super-secret-jwt-key-change-in-production"
    auth_jwt_algorithm: str = "HS256"
    auth_jwt_expire_minutes: int = 30
    
    # Rate Limiting
    rate_limit_per_min: int = 120
    rate_limit_burst: int = 10
    
    # Backend Services URLs
    user_service_url: str = "http://user:8000"
    document_service_url: str = "http://document:8000"
    topic_service_url: str = "http://topic:8000"
    llm_service_url: str = "http://llm:8000"
    conversation_service_url: str = "http://conversation:8000"
    embedding_service_url: str = "http://embedding:8000"
    
    # Redis Configuration
    redis_url: str = "redis://redis:6379/0"
    
    # Traefik Configuration
    domain: str = "localhost"
    traefik_public_network: str = "traefik-public"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()

#!/usr/bin/env python3
"""
Gateway POC Demo Script
Demonstrates all the key features of the Gateway POC
"""

import requests
import json
import time
import websocket
import threading
from typing import Dict, Any

BASE_URL = "http://localhost"


class GatewayDemo:
    def __init__(self):
        self.token = None
        self.session = requests.Session()
    
    def print_section(self, title: str):
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_response(self, response: requests.Response, title: str = "Response"):
        print(f"\n{title}:")
        print(f"Status: {response.status_code}")
        try:
            data = response.json()
            print(f"Data: {json.dumps(data, indent=2)}")
        except:
            print(f"Text: {response.text}")
    
    def demo_health_check(self):
        """Demo 1: Health Check"""
        self.print_section("Demo 1: Health Check")
        
        response = self.session.get(f"{BASE_URL}/health")
        self.print_response(response, "Health Check")
        
        response = self.session.get(f"{BASE_URL}/")
        self.print_response(response, "Root Endpoint")
    
    def demo_authentication(self):
        """Demo 2: JWT Authentication"""
        self.print_section("Demo 2: JWT Authentication")
        
        # Login
        login_data = {"username": "demo", "password": "demo123"}
        response = self.session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        self.print_response(response, "Login")
        
        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            print(f"\n✅ Authentication successful! Token: {self.token[:50]}...")
            
            # Test protected endpoint
            headers = {"Authorization": f"Bearer {self.token}"}
            response = self.session.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
            self.print_response(response, "Protected Endpoint (/auth/me)")
        else:
            print("❌ Authentication failed!")
    
    def demo_service_aggregation(self):
        """Demo 3: Service Aggregation"""
        self.print_section("Demo 3: Service Aggregation")
        
        if not self.token:
            print("❌ Authentication required for this demo")
            return
        
        headers = {"Authorization": f"Bearer {self.token}"}
        topic_id = "python-machine-learning"
        
        print(f"Aggregating data for topic: {topic_id}")
        response = self.session.get(f"{BASE_URL}/api/v1/aggregate/topic/{topic_id}", headers=headers)
        self.print_response(response, "Topic Aggregation")
        
        print("\n📊 This demonstrates how the BFF aggregates data from multiple backend services:")
        print("  • Topic Service: Topic metadata and stats")
        print("  • Document Service: Related documents")
        print("  • Conversation Service: Conversation history")
        print("  • All requests are made concurrently for optimal performance")
    
    def demo_llm_aggregation(self):
        """Demo 4: LLM Aggregation with Context"""
        self.print_section("Demo 4: LLM Aggregation with Context")
        
        if not self.token:
            print("❌ Authentication required for this demo")
            return
        
        headers = {"Authorization": f"Bearer {self.token}"}
        payload = {
            "topic_id": "python-machine-learning",
            "prompt": "What are the key concepts in machine learning?",
            "model": "gpt-3.5-turbo"
        }
        
        print(f"Sending LLM request with context...")
        print(f"Topic: {payload['topic_id']}")
        print(f"Prompt: {payload['prompt']}")
        
        response = self.session.post(f"{BASE_URL}/api/v1/aggregate/llm/generate", 
                                   json=payload, headers=headers)
        self.print_response(response, "LLM Generation with Context")
        
        print("\n🧠 This demonstrates how the BFF enhances LLM requests:")
        print("  • Retrieves relevant context from Topic and Document services")
        print("  • Enhances the prompt with historical conversation data")
        print("  • Sends enriched request to LLM service")
        print("  • Returns both the LLM response and context used")
    
    def demo_websocket(self):
        """Demo 5: WebSocket Proxy"""
        self.print_section("Demo 5: WebSocket Real-time Communication")
        
        topic_id = "real-time-chat"
        ws_url = f"ws://localhost/api/v1/ws/conversation/{topic_id}"
        
        print(f"Connecting to WebSocket: {ws_url}")
        
        def on_message(ws, message):
            data = json.loads(message)
            print(f"📨 Received: {json.dumps(data, indent=2)}")
        
        def on_open(ws):
            print("✅ WebSocket connection established")
            
            # Send a test message
            test_message = {
                "type": "user_message",
                "content": "Hello from the demo script!",
                "timestamp": time.time()
            }
            print(f"📤 Sending: {json.dumps(test_message, indent=2)}")
            ws.send(json.dumps(test_message))
            
            # Send another message after a delay
            time.sleep(1)
            test_message2 = {
                "type": "user_message", 
                "content": "This demonstrates real-time communication",
                "timestamp": time.time()
            }
            print(f"📤 Sending: {json.dumps(test_message2, indent=2)}")
            ws.send(json.dumps(test_message2))
            
            # Close after demo
            time.sleep(1)
            ws.close()
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket connection closed")
        
        def on_error(ws, error):
            print(f"❌ WebSocket error: {error}")
        
        try:
            ws = websocket.WebSocketApp(ws_url,
                                      on_open=on_open,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close)
            
            ws.run_forever()
            
            print("\n🔄 This demonstrates WebSocket proxy functionality:")
            print("  • Real-time bidirectional communication")
            print("  • Message routing through the BFF gateway")
            print("  • Support for conversation streaming")
            
        except Exception as e:
            print(f"❌ WebSocket demo failed: {str(e)}")
    
    def demo_rate_limiting(self):
        """Demo 6: Rate Limiting"""
        self.print_section("Demo 6: Rate Limiting")
        
        print("Making rapid requests to test rate limiting...")
        
        for i in range(5):
            response = self.session.get(f"{BASE_URL}/")
            print(f"Request {i+1}: Status {response.status_code}")
            if response.status_code == 429:
                print("🛡️ Rate limiting activated!")
                break
            time.sleep(0.1)
        
        print("\n🛡️ Rate limiting features:")
        print("  • Per-user rate limiting (when authenticated)")
        print("  • Per-IP rate limiting (for anonymous users)")
        print("  • Redis-backed for distributed rate limiting")
        print("  • Configurable limits and burst allowances")
    
    def run_full_demo(self):
        """Run the complete demo"""
        print("🚀 Gateway POC - Complete Feature Demo")
        print("This demo showcases all the key features of the Master-Know BFF Gateway")
        
        self.demo_health_check()
        self.demo_authentication()
        self.demo_service_aggregation()
        self.demo_llm_aggregation()
        self.demo_rate_limiting()
        self.demo_websocket()
        
        self.print_section("Demo Complete!")
        print("🎉 All Gateway POC features demonstrated successfully!")
        print("\nKey achievements:")
        print("  ✅ Traefik + FastAPI integration working")
        print("  ✅ JWT authentication implemented")
        print("  ✅ Service aggregation with concurrent requests")
        print("  ✅ LLM context enhancement")
        print("  ✅ WebSocket proxy for real-time communication")
        print("  ✅ Rate limiting with Redis backend")
        print("\n📊 Access points:")
        print("  • API Documentation: http://localhost/docs")
        print("  • Traefik Dashboard: http://localhost:8080")
        print("  • Health Check: http://localhost/health")


if __name__ == "__main__":
    demo = GatewayDemo()
    demo.run_full_demo()

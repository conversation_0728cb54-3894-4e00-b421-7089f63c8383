# Conversation Service POC

## 概述

对话管理服务的概念验证，实现智能学习对话的核心功能。

## 核心功能

1. **对话管理**
   - 创建学习对话会话
   - 管理对话历史和上下文
   - 支持多轮对话状态跟踪

2. **智能引导**
   - 基于用户知识点掌握情况生成引导问题
   - 动态调整对话策略
   - 提供个性化学习路径

3. **上下文检索**
   - 集成 Manticore Search 进行相关内容检索
   - 基于对话历史提供精准上下文
   - 支持多模态内容关联

4. **学习效果追踪**
   - 实时评估用户理解程度
   - 记录学习进度和知识点掌握
   - 生成学习报告和建议

## 技术栈

- **FastAPI**: Web 框架
- **SQLModel**: 数据模型和 ORM
- **Manticore Search**: 上下文检索
- **LLM Integration**: 智能对话生成
- **Redis**: 会话状态缓存

## 文件结构

```
conversation_poc/
├── README.md              # 本文档
├── requirements.txt       # 依赖包
├── main.py               # 主服务入口
├── models.py             # 数据模型
├── services.py           # 业务逻辑
├── test_poc.py           # POC 测试
├── run_poc.py            # 运行脚本
└── integrate_to_main.py  # 集成脚本
```

## 运行方式

```bash
# 安装依赖
pip install -r requirements.txt

# 运行 POC 测试
python test_poc.py

# 启动服务
python run_poc.py

# 集成到主系统
python integrate_to_main.py
```

## 验证目标

- [x] 对话会话创建和管理
- [x] 多轮对话上下文保持
- [x] 智能问题生成
- [x] 学习进度追踪
- [x] 与主系统集成兼容性

#!/usr/bin/env python3
"""
对话服务 POC 运行脚本
"""
import os
import sys
import time
import subprocess
import signal
from pathlib import Path


def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖...")
    
    required_packages = [
        "fastapi", "uvicorn", "sqlmodel", "pydantic", "httpx"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True


def start_service():
    """启动服务"""
    print("\n🚀 启动 Conversation Service POC...")
    
    try:
        # 启动 FastAPI 服务
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8001",
            "--reload"
        ])
        
        print(f"✅ 服务已启动 (PID: {process.pid})")
        print("📍 服务地址: http://localhost:8001")
        print("📖 API 文档: http://localhost:8001/docs")
        print("\n按 Ctrl+C 停止服务")
        
        # 等待服务启动
        time.sleep(3)
        
        # 测试服务是否正常
        test_service()
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")
        
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False
    
    return True


def test_service():
    """测试服务"""
    print("\n🧪 测试服务...")
    
    try:
        import httpx
        import asyncio
        
        async def test_health():
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:8001/health", timeout=5.0)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 健康检查通过: {data['status']}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status_code}")
                    return False
        
        # 运行健康检查
        result = asyncio.run(test_health())
        
        if result:
            print("✅ 服务运行正常")
            print("\n📋 可用的 API 端点:")
            print("- GET  /health - 健康检查")
            print("- POST /conversations - 创建对话")
            print("- GET  /conversations/{id} - 获取对话")
            print("- PUT  /conversations/{id} - 更新对话")
            print("- GET  /conversations/{id}/messages - 获取消息")
            print("- POST /conversations/{id}/chat - 发送消息")
            print("- GET  /conversations/{id}/progress - 获取进度")
        
        return result
        
    except Exception as e:
        print(f"⚠️ 服务测试失败: {e}")
        return False


def demo_usage():
    """演示用法"""
    print("\n🎯 使用示例:")
    
    demo_commands = [
        {
            "description": "创建对话",
            "method": "POST",
            "url": "http://localhost:8001/conversations",
            "data": {
                "title": "Python 学习对话",
                "learning_level": "beginner"
            }
        },
        {
            "description": "发送聊天消息",
            "method": "POST", 
            "url": "http://localhost:8001/conversations/{conversation_id}/chat",
            "data": {
                "message": "什么是 Python？",
                "context_limit": 5,
                "use_search": True
            }
        }
    ]
    
    for i, cmd in enumerate(demo_commands, 1):
        print(f"\n{i}. {cmd['description']}")
        print(f"   {cmd['method']} {cmd['url']}")
        if 'data' in cmd:
            import json
            print(f"   数据: {json.dumps(cmd['data'], indent=2, ensure_ascii=False)}")


def main():
    """主函数"""
    print("🎭 Conversation Service POC 运行器")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("main.py").exists():
        print("❌ 请在 conversation_poc 目录下运行此脚本")
        return False
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 显示使用示例
    demo_usage()
    
    # 询问是否启动服务
    print("\n" + "=" * 50)
    response = input("是否启动服务？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        return start_service()
    else:
        print("👋 未启动服务，再见！")
        return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

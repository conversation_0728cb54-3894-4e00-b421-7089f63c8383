#!/usr/bin/env python3
"""
对话服务 POC 集成到主系统脚本
"""
import os
import shutil
from pathlib import Path


def integrate_conversation_service():
    """集成对话服务到主系统"""
    print("🔄 开始集成 Conversation Service...")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent
    
    # 源目录和目标目录
    poc_dir = Path(__file__).parent
    target_service_dir = project_root / "services" / "conversation"
    target_backend_dir = project_root / "backend" / "app" / "services" / "conversation"
    
    try:
        # 1. 集成到独立服务目录
        print("📁 集成到独立服务目录...")
        
        # 复制服务文件到 services/conversation
        files_to_copy = [
            ("simple_models.py", "models.py"),
            ("services.py", "services.py"),
            ("main.py", "main.py"),
            ("requirements.txt", "requirements.txt")
        ]
        
        target_service_dir.mkdir(parents=True, exist_ok=True)
        
        for src_file, dst_file in files_to_copy:
            src_path = poc_dir / src_file
            dst_path = target_service_dir / dst_file
            
            if src_path.exists():
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制 {src_file} -> {dst_path}")
            else:
                print(f"⚠️ 源文件不存在: {src_file}")
        
        # 2. 集成到 backend 服务目录
        print("📁 集成到 backend 服务目录...")
        
        target_backend_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建 __init__.py
        init_file = target_backend_dir / "__init__.py"
        init_file.write_text('"""对话服务模块"""\n')
        
        # 复制核心服务文件
        backend_files = [
            ("services.py", "conversation_service.py"),
            ("simple_models.py", "models.py")
        ]
        
        for src_file, dst_file in backend_files:
            src_path = poc_dir / src_file
            dst_path = target_backend_dir / dst_file
            
            if src_path.exists():
                # 读取源文件内容并适配 backend 环境
                content = src_path.read_text()
                
                # 修改导入路径
                content = content.replace(
                    "from simple_models import",
                    "from .models import"
                )
                
                dst_path.write_text(content)
                print(f"✅ 适配并复制 {src_file} -> {dst_path}")
        
        # 3. 创建 API 路由
        print("📁 创建 API 路由...")
        
        api_routes_dir = project_root / "backend" / "app" / "api" / "routes"
        conversations_route = api_routes_dir / "conversations.py"
        
        if not conversations_route.exists():
            route_content = '''"""
对话管理 API 路由
"""
import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from app.api.deps import get_db, get_current_active_user
from app.models.user import User
from app.services.conversation.models import (
    ConversationCreate, ConversationUpdate, ConversationPublic,
    ConversationMessagePublic, ChatRequest, ChatResponse
)
from app.services.conversation.conversation_service import ConversationOrchestrator

router = APIRouter()

# 全局服务实例
orchestrator = ConversationOrchestrator()


@router.post("/", response_model=ConversationPublic)
def create_conversation(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    conversation_in: ConversationCreate
):
    """创建新对话"""
    conversation = orchestrator.conversation_service.create_conversation(
        current_user.id, conversation_in
    )
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@router.get("/{conversation_id}", response_model=ConversationPublic)
def get_conversation(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    conversation_id: uuid.UUID
):
    """获取对话详情"""
    conversation = orchestrator.conversation_service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    return ConversationPublic(
        id=conversation.id,
        user_id=conversation.user_id,
        title=conversation.title,
        topic_id=conversation.topic_id,
        status=conversation.status,
        learning_level=conversation.learning_level,
        context=conversation.context,
        metadata=conversation.metadata,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )


@router.post("/{conversation_id}/chat", response_model=ChatResponse)
async def chat(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    conversation_id: uuid.UUID,
    chat_request: ChatRequest
):
    """发送聊天消息"""
    try:
        response = await orchestrator.handle_chat(conversation_id, chat_request)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{conversation_id}/messages", response_model=List[ConversationMessagePublic])
def get_conversation_messages(
    *,
    session: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    conversation_id: uuid.UUID,
    limit: int = 50
):
    """获取对话消息"""
    messages = orchestrator.conversation_service.get_messages(conversation_id, limit)
    
    return [
        ConversationMessagePublic(
            id=msg.id,
            conversation_id=msg.conversation_id,
            role=msg.role,
            content=msg.content,
            context_used=msg.context_used,
            metadata=msg.metadata,
            created_at=msg.created_at
        )
        for msg in messages
    ]
'''
            conversations_route.write_text(route_content)
            print(f"✅ 创建 API 路由: {conversations_route}")
        
        # 4. 更新主路由注册
        print("📁 更新主路由注册...")
        
        main_api = project_root / "backend" / "app" / "api" / "main.py"
        if main_api.exists():
            content = main_api.read_text()
            
            # 检查是否已经包含对话路由
            if "conversations" not in content:
                # 添加导入
                if "from .routes import" in content:
                    content = content.replace(
                        "from .routes import",
                        "from .routes import conversations,"
                    )
                
                # 添加路由注册
                if 'api_router.include_router(' in content:
                    # 找到最后一个路由注册位置
                    lines = content.split('\n')
                    insert_index = -1
                    for i, line in enumerate(lines):
                        if 'api_router.include_router(' in line:
                            insert_index = i + 1
                    
                    if insert_index > 0:
                        new_route = 'api_router.include_router(conversations.router, prefix="/conversations", tags=["conversations"])'
                        lines.insert(insert_index, new_route)
                        content = '\n'.join(lines)
                
                main_api.write_text(content)
                print(f"✅ 更新主路由注册: {main_api}")
        
        print("\n🎉 Conversation Service 集成完成！")
        print("\n📋 集成结果:")
        print(f"✅ 独立服务: {target_service_dir}")
        print(f"✅ Backend 服务: {target_backend_dir}")
        print(f"✅ API 路由: {conversations_route}")
        
        print("\n🚀 下一步:")
        print("1. 重启 backend 服务")
        print("2. 访问 /docs 查看新的 API 端点")
        print("3. 测试对话功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 Conversation Service POC 集成工具")
    print("=" * 50)
    
    success = integrate_conversation_service()
    
    if success:
        print("\n✅ 集成成功完成！")
    else:
        print("\n❌ 集成失败，请检查错误信息")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

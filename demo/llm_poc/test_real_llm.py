import asyncio
import httpx
import json

async def test_real_llm():
    """测试真实的 LLM 服务调用"""
    
    # 测试用例 1: 简单问答
    print("🤖 测试 1: 简单问答")
    print("=" * 50)
    request_data = {
        "conversation_id": "real-test-001",
        "user_query": "你好，请简单介绍一下什么是人工智能？",
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 生成成功!")
                print(f"📄 回答:\n{result['text']}")
                print(f"📊 Token 使用: {result['usage']}")
                print(f"📝 元数据: {result['metadata']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    print("\n" + "=" * 50)
    
    # 测试用例 2: 带对话历史
    print("🤖 测试 2: 多轮对话")
    print("=" * 50)
    request_data_2 = {
        "conversation_id": "real-test-002",
        "user_query": "基于刚才的回答，请告诉我AI在日常生活中的3个具体应用例子",
        "history": [
            {
                "role": "user",
                "content": "什么是人工智能？"
            },
            {
                "role": "assistant", 
                "content": "人工智能(AI)是指让机器模拟人类智能行为的技术，包括学习、推理、感知等能力。"
            }
        ],
        "max_tokens": 300,
        "temperature": 0.5
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json=request_data_2
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 多轮对话成功!")
                print(f"📄 回答:\n{result['text']}")
                print(f"📊 Token 使用: {result['usage']}")
                print(f"📝 模型: {result['metadata']['model']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    print("\n" + "=" * 50)
    
    # 测试用例 3: 编程相关问题
    print("🤖 测试 3: 编程问题")
    print("=" * 50)
    request_data_3 = {
        "conversation_id": "real-test-003", 
        "user_query": "请写一个Python函数来计算斐波那契数列的第n项",
        "max_tokens": 250,
        "temperature": 0.3
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/llm/generate",
                json=request_data_3
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 编程问题测试成功!")
                print(f"📄 代码回答:\n{result['text']}")
                print(f"📊 Token 使用: {result['usage']}")
                print(f"🏁 结束原因: {result['metadata']['finish_reason']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    print("\n🎉 所有真实 LLM 测试完成!")
    return True

if __name__ == "__main__":
    print("🚀 LLM Integration POC - 真实服务测试")
    print("测试真实的 OpenAI API 调用...")
    print()
    
    asyncio.run(test_real_llm())
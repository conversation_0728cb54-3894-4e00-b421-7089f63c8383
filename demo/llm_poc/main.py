import os
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import httpx
import openai
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="LLM Integration POC", version="1.0.0")

class Message(BaseModel):
    role: str = Field(description="Message role: system, user, or assistant")
    content: str = Field(description="Message content")

class GenerateRequest(BaseModel):
    conversation_id: str = Field(description="Conversation ID for context retrieval")
    user_query: str = Field(description="User query to process")
    history: Optional[List[Message]] = Field(default=[], description="Conversation history")
    max_tokens: Optional[int] = Field(default=4096, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(default=0.2, description="Generation temperature")

class GenerateResponse(BaseModel):
    text: str = Field(description="Generated response")
    usage: dict = Field(description="Token usage information")
    metadata: dict = Field(description="Additional metadata")

class OpenAIProvider:
    def __init__(self, api_key: str, base_url: str = None, model: str = "gpt-4o-mini"):
        client_kwargs = {"api_key": api_key}
        if base_url:
            client_kwargs["base_url"] = base_url
        
        self.client = openai.AsyncOpenAI(**client_kwargs)
        self.model = model
    
    async def generate(self, messages: List[dict], max_tokens: int = 4096, temperature: float = 0.2) -> dict:
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return {
                "text": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": response.model,
                "finish_reason": response.choices[0].finish_reason
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"OpenAI API error: {str(e)}")

# 初始化 provider
openai_provider = OpenAIProvider(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_BASE_URL"),
    model=os.getenv("OPENAI_MODEL", "gpt-4o-mini")
)

async def fetch_long_term_memory(conversation_id: str) -> str:
    """模拟从 Manticore 获取长期记忆"""
    # TODO: 实际实现中会调用 Manticore 异步客户端
    return f"Long-term memory context for conversation {conversation_id}"

def build_prompt(system_prompt: str, context: str, history: List[Message], user_query: str) -> List[dict]:
    """构建完整的对话 prompt"""
    messages = []
    
    # 系统提示
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    
    # 长期记忆上下文
    if context:
        messages.append({"role": "system", "content": f"Context: {context}"})
    
    # 对话历史
    for msg in history:
        messages.append({"role": msg.role, "content": msg.content})
    
    # 当前用户查询
    messages.append({"role": "user", "content": user_query})
    
    return messages

@app.post("/api/v1/llm/generate", response_model=GenerateResponse)
async def generate(request: GenerateRequest):
    """生成 LLM 响应的主要接口"""
    try:
        # 1. 获取长期记忆上下文
        context = await fetch_long_term_memory(request.conversation_id)
        
        # 2. 构建系统提示
        system_prompt = "You are a helpful AI assistant. Use the provided context to give relevant and accurate responses."
        
        # 3. 构建完整 prompt
        messages = build_prompt(
            system_prompt=system_prompt,
            context=context,
            history=request.history,
            user_query=request.user_query
        )
        
        # 4. 调用 LLM 生成
        result = await openai_provider.generate(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # 5. 构造响应
        return GenerateResponse(
            text=result["text"],
            usage=result["usage"],
            metadata={
                "conversation_id": request.conversation_id,
                "model": result["model"],
                "finish_reason": result["finish_reason"]
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Generation error: {str(e)}")

@app.get("/health")
async def health():
    """健康检查接口"""
    return {"status": "healthy", "service": "llm-integration-poc"}

@app.get("/")
async def root():
    """根路径信息"""
    return {
        "service": "LLM Integration POC",
        "version": "1.0.0",
        "endpoints": {
            "generate": "/api/v1/llm/generate",
            "health": "/health"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
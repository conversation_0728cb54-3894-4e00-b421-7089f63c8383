#!/usr/bin/env python3
"""
简化的Manticore Search测试脚本

直接使用HTTP API测试基本功能
"""

import asyncio
import aiohttp
import json
import numpy as np

async def test_manticore():
    """测试Manticore基本功能"""
    base_url = "http://127.0.0.1:9308"
    
    async with aiohttp.ClientSession() as session:
        print("🚀 开始Manticore测试")
        
        # 1. 测试连接
        print("\n1. 测试连接...")
        async with session.get(f"{base_url}/") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 连接成功，版本: {data.get('version', {}).get('number', 'Unknown')}")
            else:
                print(f"❌ 连接失败: {response.status}")
                return
        
        # 2. 创建表
        print("\n2. 创建表...")
        create_sql = """CREATE TABLE IF NOT EXISTS test_docs (
            id string,
            content text,
            embedding float_vector(384)
        ) engine='columnar'"""
        
        async with session.post(
            f"{base_url}/cli",
            data=create_sql,
            headers={'Content-Type': 'text/plain'}
        ) as response:
            if response.status == 200:
                print("✅ 表创建成功")
            else:
                error = await response.text()
                print(f"❌ 表创建失败: {error}")
        
        # 3. 插入文档
        print("\n3. 插入文档...")
        
        # 生成示例向量
        embedding = np.random.normal(0, 1, 384).tolist()
        
        doc = {
            "index": "test_docs",
            "doc": {
                "id": "doc1",
                "content": "人工智能是计算机科学的重要分支",
                "embedding": embedding
            }
        }
        
        async with session.post(
            f"{base_url}/insert",
            json=doc,
            headers={'Content-Type': 'application/json'}
        ) as response:
            if response.status == 200:
                result = await response.json()
                print(f"✅ 文档插入成功: {result}")
            else:
                error = await response.text()
                print(f"❌ 文档插入失败: {error}")
        
        # 4. 搜索文档
        print("\n4. 搜索文档...")
        search_query = {
            "index": "test_docs",
            "query": {
                "match": {
                    "content": "人工智能"
                }
            }
        }
        
        async with session.post(
            f"{base_url}/search",
            json=search_query,
            headers={'Content-Type': 'application/json'}
        ) as response:
            if response.status == 200:
                result = await response.json()
                hits = result.get('hits', {}).get('hits', [])
                print(f"✅ 搜索成功，找到 {len(hits)} 条结果")
                for hit in hits:
                    source = hit.get('_source', {})
                    score = hit.get('_score', 0)
                    print(f"   - ID: {source.get('id')}, Score: {score}")
            else:
                error = await response.text()
                print(f"❌ 搜索失败: {error}")
        
        # 5. 查看表状态
        print("\n5. 查看表状态...")
        async with session.post(
            f"{base_url}/cli",
            data="SELECT COUNT(*) FROM test_docs",
            headers={'Content-Type': 'text/plain'}
        ) as response:
            if response.status == 200:
                result = await response.text()
                print(f"✅ 表状态: {result}")
            else:
                error = await response.text()
                print(f"❌ 查询失败: {error}")

if __name__ == "__main__":
    asyncio.run(test_manticore())

#!/usr/bin/env python3
"""
调试响应对象结构
"""

import asyncio
import manticoresearch
from pprint import pprint

async def debug_response():
    configuration = manticoresearch.Configuration(host="http://127.0.0.1:9308")
    
    async with manticoresearch.ApiClient(configuration) as api_client:
        searchApi = manticoresearch.SearchApi(api_client)
        
        # 执行搜索
        search_response = await searchApi.search({
            "table": "async_products",
            "query": {"match_all": {}}
        })
        
        print("响应对象类型:", type(search_response))
        print("响应对象属性:", dir(search_response))
        print("\n响应对象内容:")
        pprint(search_response)
        
        # 尝试访问不同的属性
        if hasattr(search_response, 'hits'):
            print(f"\n直接访问 hits: {search_response.hits}")
        
        if hasattr(search_response, 'to_dict'):
            print(f"\n转换为字典: {search_response.to_dict()}")
        
        if hasattr(search_response, '__dict__'):
            print(f"\n对象字典: {search_response.__dict__}")

if __name__ == "__main__":
    asyncio.run(debug_response())

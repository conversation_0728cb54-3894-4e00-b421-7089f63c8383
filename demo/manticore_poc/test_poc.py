#!/usr/bin/env python3
"""
Manticore Search POC 测试脚本

根据 demo/manticore_poc/README.md 验证清单实现的自动化测试。
确保 POC 的所有功能都能正常工作。

使用方法:
    pytest demo/manticore_poc/test_poc.py -v
    或
    python3 demo/manticore_poc/test_poc.py

测试内容:
1. Manticore 服务连接测试
2. 索引创建和管理测试
3. 文档插入和检索测试
4. 关键词搜索测试
5. 向量搜索测试
6. 幂等性操作测试
"""

import sys
import asyncio
import pytest
import os
from pathlib import Path
from typing import List, Dict, Any
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 加载环境变量
load_dotenv(Path(__file__).parent / '.env')

try:
    import manticoresearch
    from manticoresearch.rest import ApiException
    import numpy as np
    HAS_MANTICORE = True
except ImportError:
    HAS_MANTICORE = False

# 导入主程序
from demo.manticore_poc.main import ManticorePOC

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def manticore_poc():
    """创建 ManticorePOC 实例"""
    if not HAS_MANTICORE:
        pytest.skip("缺少 manticoresearch 依赖")
    
    poc = ManticorePOC()
    
    # 确保连接正常
    connected = await poc.test_connection()
    if not connected:
        pytest.skip("无法连接到 Manticore 服务")
    
    return poc

class TestManticoreConnection:
    """测试 Manticore 连接"""
    
    @pytest.mark.asyncio
    async def test_connection_success(self, manticore_poc):
        """测试连接成功"""
        result = await manticore_poc.test_connection()
        assert result is True, "应该能够成功连接到 Manticore 服务"
    
    @pytest.mark.asyncio
    async def test_configuration_valid(self, manticore_poc):
        """测试配置有效性"""
        assert manticore_poc.host is not None
        assert manticore_poc.http_port > 0
        assert manticore_poc.sql_port > 0
        assert manticore_poc.embedding_dim > 0

class TestTableOperations:
    """测试表操作"""
    
    @pytest.mark.asyncio
    async def test_create_docs_chunks_table(self, manticore_poc):
        """测试创建 docs_chunks 表"""
        result = await manticore_poc.create_docs_chunks_table()
        assert result is True, "应该能够成功创建 docs_chunks 表"
    
    @pytest.mark.asyncio
    async def test_table_exists_after_creation(self, manticore_poc):
        """测试表创建后存在性"""
        # 先创建表
        await manticore_poc.create_docs_chunks_table()
        
        # 检查表统计信息
        stats = await manticore_poc.get_table_stats()
        assert stats.get('table_exists') is True, "表应该存在"

class TestDocumentOperations:
    """测试文档操作"""
    
    @pytest.mark.asyncio
    async def test_insert_sample_chunks(self, manticore_poc):
        """测试插入示例文档块"""
        # 先创建表
        await manticore_poc.create_docs_chunks_table()
        
        # 插入文档块
        result = await manticore_poc.insert_sample_chunks()
        assert result is True, "应该能够成功插入文档块"
    
    @pytest.mark.asyncio
    async def test_document_count_after_insert(self, manticore_poc):
        """测试插入后文档数量"""
        # 创建表并插入文档
        await manticore_poc.create_docs_chunks_table()
        await manticore_poc.insert_sample_chunks()
        
        # 检查文档数量
        stats = await manticore_poc.get_table_stats()
        total_docs = stats.get('total_documents', 0)
        assert total_docs >= 3, f"应该至少有3个文档，实际有 {total_docs} 个"
    
    @pytest.mark.asyncio
    async def test_embedding_generation(self, manticore_poc):
        """测试向量生成"""
        text = "测试文本"
        embedding = manticore_poc.generate_sample_embedding(text)
        
        assert isinstance(embedding, list), "向量应该是列表类型"
        assert len(embedding) == manticore_poc.embedding_dim, f"向量维度应该是 {manticore_poc.embedding_dim}"
        assert all(isinstance(x, float) for x in embedding), "向量元素应该都是浮点数"

class TestSearchOperations:
    """测试搜索操作"""
    
    @pytest.mark.asyncio
    async def test_keyword_search(self, manticore_poc):
        """测试关键词搜索"""
        # 准备数据
        await manticore_poc.create_docs_chunks_table()
        await manticore_poc.insert_sample_chunks()
        
        # 执行搜索测试
        result = await manticore_poc.test_keyword_search()
        assert result is True, "关键词搜索应该成功"
    
    @pytest.mark.asyncio
    async def test_vector_search(self, manticore_poc):
        """测试向量搜索"""
        # 准备数据
        await manticore_poc.create_docs_chunks_table()
        await manticore_poc.insert_sample_chunks()
        
        # 执行向量搜索测试
        result = await manticore_poc.test_vector_search()
        assert result is True, "向量搜索应该成功"
    
    @pytest.mark.asyncio
    async def test_search_results_format(self, manticore_poc):
        """测试搜索结果格式"""
        # 准备数据
        await manticore_poc.create_docs_chunks_table()
        await manticore_poc.insert_sample_chunks()
        
        # 执行搜索并检查结果格式
        async with manticoresearch.ApiClient(manticore_poc.configuration) as api_client:
            search_api = manticoresearch.SearchApi(api_client)
            
            search_request = {
                "index": "docs_chunks",
                "query": {
                    "query_string": "@content 人工智能"
                },
                "limit": 5
            }
            
            results = await search_api.search(search_request)
            
            # 验证结果结构
            assert 'hits' in results, "结果应该包含 hits 字段"
            assert 'hits' in results['hits'], "hits 应该包含 hits 数组"
            
            if results['hits']['hits']:
                hit = results['hits']['hits'][0]
                assert '_source' in hit, "每个结果应该包含 _source 字段"
                assert '_score' in hit, "每个结果应该包含 _score 字段"
                
                source = hit['_source']
                assert 'id' in source, "source 应该包含 id 字段"
                assert 'content' in source, "source 应该包含 content 字段"

class TestIdempotency:
    """测试幂等性"""
    
    @pytest.mark.asyncio
    async def test_idempotency_operations(self, manticore_poc):
        """测试幂等性操作"""
        # 准备数据
        await manticore_poc.create_docs_chunks_table()
        
        # 执行幂等性测试
        result = await manticore_poc.test_idempotency()
        assert result is True, "幂等性测试应该通过"
    
    @pytest.mark.asyncio
    async def test_replace_vs_insert(self, manticore_poc):
        """测试 replace 和 insert 的区别"""
        # 准备数据
        await manticore_poc.create_docs_chunks_table()
        
        async with manticoresearch.ApiClient(manticore_poc.configuration) as api_client:
            index_api = manticoresearch.IndexApi(api_client)
            search_api = manticoresearch.SearchApi(api_client)
            
            # 测试文档
            test_doc = {
                "id": "replace_test",
                "doc_id": "test",
                "content": "原始内容",
                "metadata": {"version": 1},
                "embedding": manticore_poc.generate_sample_embedding("原始内容")
            }
            
            # 第一次插入
            await index_api.replace({
                "index": "docs_chunks",
                "doc": test_doc
            })
            
            # 修改内容后再次 replace
            test_doc["content"] = "更新内容"
            test_doc["metadata"]["version"] = 2
            test_doc["embedding"] = manticore_poc.generate_sample_embedding("更新内容")
            
            await index_api.replace({
                "index": "docs_chunks",
                "doc": test_doc
            })
            
            # 验证只有一条记录且内容已更新
            search_request = {
                "index": "docs_chunks",
                "query": {
                    "query_string": "@id replace_test"
                }
            }
            
            results = await search_api.search(search_request)
            hits = results.get('hits', {}).get('hits', [])
            
            assert len(hits) == 1, "应该只有一条记录"
            
            source = hits[0]['_source']
            assert source['content'] == "更新内容", "内容应该已更新"
            assert source['metadata']['version'] == 2, "版本应该已更新"

class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_workflow(self, manticore_poc):
        """测试完整工作流程"""
        # 1. 创建表
        create_result = await manticore_poc.create_docs_chunks_table()
        assert create_result is True
        
        # 2. 插入文档
        insert_result = await manticore_poc.insert_sample_chunks()
        assert insert_result is True
        
        # 3. 关键词搜索
        keyword_result = await manticore_poc.test_keyword_search()
        assert keyword_result is True
        
        # 4. 向量搜索
        vector_result = await manticore_poc.test_vector_search()
        assert vector_result is True
        
        # 5. 幂等性测试
        idempotency_result = await manticore_poc.test_idempotency()
        assert idempotency_result is True
        
        # 6. 验证最终状态
        stats = await manticore_poc.get_table_stats()
        assert stats.get('table_exists') is True
        assert stats.get('total_documents', 0) >= 3

def run_tests_directly():
    """直接运行测试（不使用 pytest）"""
    async def run_all_tests():
        print("🧪 开始运行 Manticore POC 测试")
        print("=" * 60)
        
        if not HAS_MANTICORE:
            print("❌ 缺少必要依赖，请先安装 requirements.txt")
            return False
        
        poc = ManticorePOC()
        
        # 检查连接
        if not await poc.test_connection():
            print("❌ 无法连接到 Manticore 服务")
            return False
        
        # 测试列表
        tests = [
            ("连接测试", lambda: poc.test_connection()),
            ("表创建测试", lambda: poc.create_docs_chunks_table()),
            ("文档插入测试", lambda: poc.insert_sample_chunks()),
            ("关键词搜索测试", lambda: poc.test_keyword_search()),
            ("向量搜索测试", lambda: poc.test_vector_search()),
            ("幂等性测试", lambda: poc.test_idempotency())
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 运行: {test_name}")
            try:
                result = await test_func()
                if result:
                    passed += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
        
        # 输出结果
        print("\n" + "=" * 60)
        print(f"🧪 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("⚠️  部分测试失败")
            return False
    
    return asyncio.run(run_all_tests())

if __name__ == "__main__":
    success = run_tests_directly()
    sys.exit(0 if success else 1)

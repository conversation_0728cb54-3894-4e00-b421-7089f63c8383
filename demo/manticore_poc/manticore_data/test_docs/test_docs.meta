{"meta_created_time_utc": "2025-08-15T01:43:26.070", "meta_version": 20, "total_documents": 1, "total_bytes": 45, "tid": 1, "schema": {"fields": [{"name": "content", "flags": 3}], "attributes": [{"name": "id", "type": 6, "locator": {"pos": 0, "bits": 64}}, {"name": "$_blob_locator", "engine": 1, "type": 6, "locator": {"pos": 64, "bits": 64}}, {"name": "embedding", "type": 12, "locator": {"pos": 0, "bits": 0}}]}, "index_settings": {"hit_format": 1, "blob_update_space": 131072}, "tokenizer_settings": {"type": 2, "case_folding": "non_cont"}, "dictionary_settings": {}, "words_checkpoint": 48, "max_codepoint_length": 3, "chunk_names": [], "soft_ram_limit": 134217728}
[Thu Aug 14 10:48:59.801 2025] [1] Using local time zone 'UTC'
[Thu Aug 14 10:48:59.802 2025] [1] starting daemon version '13.6.7 1ab9ccc95@25080704 (columnar 8.0.1 fc30df6@25072219) (secondary 8.0.1 fc30df6@25072219) (knn 8.0.1 fc30df6@25072219) (embeddings 1.0.0)' ...
[Thu Aug 14 10:48:59.802 2025] [1] listening on all interfaces for mysql, port=9306
[Thu Aug 14 10:48:59.802 2025] [1] listening on UNIX socket /var/run/mysqld/mysqld.sock
[Thu Aug 14 10:48:59.803 2025] [1] listening on all interfaces for sphinx and http(s), port=9308
[Thu Aug 14 10:48:59.803 2025] [1] listening on **********:9312 for sphinx and http(s)
[Thu Aug 14 10:48:59.804 2025] [52] prereading 0 tables
[Thu Aug 14 10:48:59.805 2025] [52] preread 0 tables in 0.000 sec
[Thu Aug 14 10:48:59.829 2025] [1] accepting connections
[Thu Aug 14 10:48:59.909 2025] [55] [BUDDY] started v3.34.2+25080617-ff540bfc 'manticore-executor /usr/share/manticore/modules/manticore-buddy/src/main.php --listen=http://0.0.0.0:9308   --threads=10 --skip=manticoresoftware/buddy-plugin-sharding --skip=manticoresoftware/buddy-plugin-distributed-insert ' at http://127.0.0.1:35449
[Thu Aug 14 10:48:59.910 2025] [55] [BUDDY] Loaded plugins:
[Thu Aug 14 10:48:59.910 2025] [55] [BUDDY]   core: empty-string, backup, emulate-elastic, fuzzy, create-table, create-cluster, drop, insert, alias, select, show, plugin, test, alter-column, alter-distributed-table, alter-rename-table, modify-table, knn, replace, queue, update, autocomplete, cli-table, truncate, metrics
[Thu Aug 14 10:48:59.910 2025] [55] [BUDDY]   local: 
[Thu Aug 14 10:48:59.910 2025] [55] [BUDDY]   extra: 
[Thu Aug 14 20:51:50.658 2025] [49] rt: table localhost_test: ramchunk saved ok (mode=periodic, last TID=0, current TID=1, ram=0.000 Mb, time delta=29224 sec, took=0.033 sec)
[Thu Aug 14 20:51:50.659 2025] [54] rt: table test_docs: ramchunk saved ok (mode=periodic, last TID=0, current TID=1, ram=0.003 Mb, time delta=35791 sec, took=0.034 sec)
[Thu Aug 14 20:51:50.660 2025] [56] rt: table async_products: ramchunk saved ok (mode=periodic, last TID=0, current TID=5, ram=0.001 Mb, time delta=34774 sec, took=0.034 sec)
[Thu Aug 14 20:51:50.660 2025] [55] rt: table service_localhost_test: ramchunk saved ok (mode=periodic, last TID=0, current TID=2, ram=0.000 Mb, time delta=29224 sec, took=0.035 sec)
[Fri Aug 15 01:43:25.476 2025] [1] caught SIGTERM, shutting down
[Fri Aug 15 01:43:25.480 2025] [52] WARNING: conn 127.0.0.1:52190(158), sock=17: failed to receive HTTP request, AsyncNetInputBuffer_c::AppendData: got SIGTERM, return -1
[Fri Aug 15 01:43:26.069 2025] [56] rt: table docs_chunks: ramchunk saved in 0.005 sec
[Fri Aug 15 01:43:26.072 2025] [56] rt: table test_docs: ramchunk saved in 0.002 sec
[Fri Aug 15 01:43:26.074 2025] [56] rt: table async_products: ramchunk saved in 0.002 sec
[Fri Aug 15 01:43:26.078 2025] [56] rt: table localhost_test: ramchunk saved in 0.003 sec
[Fri Aug 15 01:43:26.082 2025] [56] rt: table service_localhost_test: ramchunk saved in 0.003 sec
[Fri Aug 15 01:43:26.084 2025] [1] shutdown daemon version '13.6.7 1ab9ccc95@25080704 (columnar 8.0.1 fc30df6@25072219) (secondary 8.0.1 fc30df6@25072219) (knn 8.0.1 fc30df6@25072219) (embeddings 1.0.0) (buddy v3.34.2+25080617-ff540bfc)' ...
[Fri Aug 15 01:43:26.084 2025] [1] shutdown complete
